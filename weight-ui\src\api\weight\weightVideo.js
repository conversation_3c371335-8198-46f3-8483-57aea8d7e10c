import request from '@/utils/request'

// 查询过磅视频列表
export function listWeightVideo(query) {
  return request({
    url: '/weight/weightVideo/list',
    method: 'get',
    params: query
  })
}

// 查询过磅视频详细
export function getWeightVideo(weightVideoId) {
  return request({
    url: '/weight/weightVideo/' + weightVideoId,
    method: 'get'
  })
}

// 新增过磅视频
export function addWeightVideo(data) {
  return request({
    url: '/weight/weightVideo',
    method: 'post',
    data: data
  })
}

// 修改过磅视频
export function updateWeightVideo(data) {
  return request({
    url: '/weight/weightVideo',
    method: 'put',
    data: data
  })
}

// 删除过磅视频
export function delWeightVideo(weightVideoId) {
  return request({
    url: '/weight/weightVideo/' + weightVideoId,
    method: 'delete'
  })
}
