<template>
<el-dropdown trigger="click" @command="handleSetSize">
    <div>
        <svg-icon class-name="el-icon-s-comment" icon-class="language" />
    </div>
    <el-dropdown-menu slot="dropdown">
        <!-- <el-dropdown-item v-for="item of sizeOptions" :key="item.value"  :command="item.value">
            {{ item.label }}
        </el-dropdown-item> -->
        <div id="translate" style="margin-top: 0.5rem;"></div>
    </el-dropdown-menu>
</el-dropdown>
</template>

<script>
export default {
    data() {
        return {
            sizeOptions: [{
                    label: 'English',
                    value: 'english'
                },
                {
                    label: '简体中文',
                    value: 'chinese_simplified'
                },
                {
                    label: '繁體中文',
                    value: 'chinese_traditional'
                },
                {
                    label: '한국어',
                    value: 'korean'
                },
                {
                    label: 'しろうと',
                    value: 'japanese'
                },
                {
                    label: 'Tiếng Việt',
                    value: 'vietnamese'
                },
                // {
                //     label: 'हिन्दी',
                //     value: 'hindi'
                // },
                // {
                //     label: 'Русский язык',
                //     value: 'russian'
                // },
                // {
                //     label: 'Français',
                //     value: 'french'
                // },
                // {
                //     label: 'УкраїнськаName',
                //     value: 'ukrainian'
                // },
                // {
                //     label: 'Norge',
                //     value: 'norwegian'
                // },
                // {
                //     label: 'color name',
                //     value: 'welsh'
                // },
                // {
                //     label: 'nederlands',
                //     value: 'dutch'
                // },
                // {
                //     label: 'Pilipino',
                //     value: 'filipino'
                // },
                // {
                //     label: 'ກະຣຸນາ',
                //     value: 'lao'
                // },
                // {
                //     label: 'తెలుగుQFontDatabase',
                //     value: 'telugu'
                // },
                // {
                //     label: 'Română',
                //     value: 'romanian'
                // },
                // {
                //     label: 'नेपालीName',
                //     value: 'nepali'
                // },
                // {
                //     label: 'Kreyòl ayisyen',
                //     value: 'haitian_creole'
                // },
                // {
                //     label: 'český',
                //     value: 'czech'
                // },
                // {
                //     label: 'Svenska',
                //     value: 'swedish'
                // },
                // {
                //     label: 'Malagasy',
                //     value: 'malagasy'
                // },
                // {
                //     label: 'ဗာရမ်',
                //     value: 'burmese'
                // },
                // {
                //     label: 'پښتوName',
                //     value: 'pashto'
                // },
                // {
                //     label: 'คนไทย',
                //     value: 'thai'
                // },
                // {
                //     label: 'Արմենյան',
                //     value: 'armenian'
                // },
                // {
                //     label: 'Persian',
                //     value: 'persian'
                // },
                // {
                //     label: 'Kurdî',
                //     value: 'kurdish'
                // },
                // {
                //     label: 'Türkçe',
                //     value: 'turkish'
                // },
                // {
                //     label: 'български',
                //     value: 'bulgarian'
                // },
                // {
                //     label: 'Malay',
                //     value: 'malay'
                // },
                // {
                //     label: 'Kiswahili',
                //     value: 'swahili'
                // },
                // {
                //     label: 'ଓଡିଆ',
                //     value: 'oriya'
                // },
                // {
                //     label: 'ÍslandName',
                //     value: 'icelandic'
                // },
                // {
                //     label: 'Íris',
                //     value: 'irish'
                // },
                // {
                //     label: 'ខ្មែរ',
                //     value: 'khmer'
                // },
                // {
                //     label: 'ગુજરાતી',
                //     value: 'gujarati'
                // },
                // {
                //     label: 'Slovenská',
                //     value: 'slovak'
                // },
                // {
                //     label: 'ಕನ್ನಡ್Name',
                //     value: 'kannada'
                // },
                // {
                //     label: 'היברית',
                //     value: 'hebrew'
                // },
                // {
                //     label: 'magyar',
                //     value: 'hungarian'
                // },
                // {
                //     label: 'मराठीName',
                //     value: 'marathi'
                // },
                // {
                //     label: 'தாமில்',
                //     value: 'tamil'
                // },
                // {
                //     label: 'eesti keel',
                //     value: 'estonian'
                // },
                // {
                //     label: 'മലമാലം',
                //     value: 'malayalam'
                // },
                // {
                //     label: 'ᐃᓄᒃᑎᑐᑦ',
                //     value: 'inuktitut'
                // },
                // {
                //     label: 'بالعربية',
                //     value: 'arabic'
                // },
                // {
                //     label: 'Deutsch',
                //     value: 'deutsch'
                // },
                // {
                //     label: 'slovenščina',
                //     value: 'slovene'
                // },
                // {
                //     label: 'বেঙ্গালী',
                //     value: 'bengali'
                // },
                // {
                //     label: 'اوردو',
                //     value: 'urdu'
                // },
                // {
                //     label: 'azerbaijani',
                //     value: 'azerbaijani'
                // },
                // {
                //     label: 'português',
                //     value: 'portuguese'
                // },
                // {
                //     label: 'lifiava',
                //     value: 'samoan'
                // },
                // {
                //     label: 'afrikaans',
                //     value: 'afrikaans'
                // },
                // {
                //     label: '汤加语',
                //     value: 'tongan'
                // },
                // {
                //     label: 'ελληνικά',
                //     value: 'greek'
                // },
                // {
                //     label: 'IndonesiaName',
                //     value: 'indonesian'
                // },
                // {
                //     label: 'Español',
                //     value: 'spanish'
                // },
                // {
                //     label: 'dansk',
                //     value: 'danish'
                // },
                // {
                //     label: 'amharic',
                //     value: 'amharic'
                // },
                // {
                //     label: 'ਪੰਜਾਬੀName',
                //     value: 'punjabi'
                // },
                // {
                //     label: 'albanian',
                //     value: 'albanian'
                // },
                // {
                //     label: 'Lietuva',
                //     value: 'lithuanian'
                // },
                // {
                //     label: 'italiano',
                //     value: 'italian'
                // },
                // {
                //     label: 'Malti',
                //     value: 'maltese'
                // },
                // {
                //     label: 'suomi',
                //     value: 'finnish'
                // },
                // {
                //     label: 'català',
                //     value: 'catalan'
                // },
                // {
                //     label: 'hrvatski',
                //     value: 'croatian'
                // },
                // {
                //     label: 'bosnian',
                //     value: 'bosnian'
                // },
                // {
                //     label: 'Polski',
                //     value: 'polish'
                // },
                // {
                //     label: 'latviešu',
                //     value: 'latvian'
                // },
                // {
                //     label: 'Maori',
                //     value: 'maori'
                // }
            ],
            localLanguage:this.$translate.language.getLocal(),
            currentLanguage : '',
        }
    },
    mounted() {
      this.$translate.language.setLocal('chinese_simplified');
      this.$translate.service.use('client.edge');
      this.$translate.language.setUrlParamControl();
      this.$translate.listener.start(); // 监听翻译
      this.sizeOptions.forEach(element => {
        this.$translate.ignore.text.push(element.label)
      });
      this.$translate.whole.enableAll(); // 启用全部翻译
      this.$translate.execute()
      this.$translate.request.listener.start();
    },
    methods: {
        handleSetSize(lang) {

        },
    }

}
</script>
