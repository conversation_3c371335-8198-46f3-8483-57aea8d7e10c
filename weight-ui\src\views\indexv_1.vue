<template>
<div class="box">
    <div ref="vantaRef" style="width: 100%; height: calc(100vh-84px)"></div>
    <div class="banner">
        <h1>华星称重管理系统</h1>
        <h6>
            &ensp;&ensp;HUA&ensp;&ensp;&ensp;XING&ensp;&ensp;&ensp;&ensp;CHENG&ensp;&ensp;&ensp;&ensp;&ensp;ZHONG
        </h6>
    </div>
</div>
</template>

<script>
import * as THREE from "three";
import RINGS from "vanta/src/vanta.rings";

export default {
    data() {
        return {};
    },
    mounted() {
        this.vantaEffect = RINGS({
            el: this.$refs.vantaRef,
            THREE: THREE,
        });
        VANTA.RINGS({
            el: this.$refs.vantaRef,
            /*以下为样式配置*/
            mouseControls: true,
            touchControls: true,
            gyroControls: false,
            minHeight: 200.0,
            minWidth: 200.0,
            scale: 1.0, //比例
            scaleMobile: 1.0,
            backgroundColor: 0x0,
            color: 0xb0d29d,
            backgroundAlpha: 0.84,
        });
    },
    beforeDestroy() {
        if (this.vantaEffect) {
            this.vantaEffect.destroy();
        }
    },
};
</script>

<style lang="scss" scoped>
.box {
    position: relative;

    .banner {
        z-index: 999;
        position: absolute;
        top: 30%;
        left: 10%;
        color: #fff;
    }
}

h1 {
    font-size: 66px;
}

p {
    margin-top: 60px;
    font-size: 18px;
}
</style>
