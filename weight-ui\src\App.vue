<template>
<div id="app" class="app-container" :style="{'--theme': $store.state.settings.theme}">
    <router-view />
    <theme-picker />
</div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";

export default {
    name: "App",
    components: {
        ThemePicker
    },
    data() {
        return {
            isDevToolsOpen: false,
        };
    },
    computed: {},
    watch: {},
    created() {
        // this.checkDevTools();
        // 添加随机参数强制跳过缓存
        // window.location.href = window.location.origin + window.location.pathname + '?t=' + Date.now();

        // 如果需保留原有参数
        // const url = new URL(window.location.href);
        // url.searchParams.set('t', Date.now()); // 添加时间戳参数
        // window.location.href = url.toString();
    },
    mounted() {},
    methods: {
        checkDevTools() {
            // 使用更可靠的检测方式
            const devtools = /./;
            devtools.toString = function () {
                debugger; // 直接触发断点
                return '';
            }
            // 如果上面的方法不触发，则使用轮询检查
            if (window.outerWidth - window.innerWidth > 160 || window.outerHeight - window.innerHeight > 160) {
                debugger;
            } else {
                this.isDevToolsOpen = false;
            }
            setTimeout(this.checkDevTools, 200);
        },
    },
    metaInfo() {
        return {
            title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
            titleTemplate: title => {
                return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
            }
        }
    }
};
</script>

<style scoped>
#app .theme-picker {
    display: none;
}

.app {
    background-color: var(--theme) !important;
}
</style>
