<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="日志创建时间" prop="createTime">
            <el-date-picker clearable v-model="queryParams.createTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择日志创建时间">
            </el-date-picker>
        </el-form-item>
        <el-form-item label="发送方" prop="sender">
            <el-input v-model="queryParams.sender" placeholder="请输入发送方" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="接收方" prop="receiver">
            <el-input v-model="queryParams.receiver" placeholder="请输入接收方" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:reportLog:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:reportLog:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:reportLog:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:reportLog:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="reportLogList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="日志唯一ID" align="center" prop="logId" />
        <el-table-column label="报文方向" align="center" prop="direction" />
        <el-table-column label="报文类型" align="center" prop="msgType" />
        <el-table-column label="报文描述" align="center" prop="msgDesc" />
        <el-table-column label="原始报文内容" align="center" prop="rawMessage" />
        <el-table-column label="发送方" align="center" prop="sender" />
        <el-table-column label="接收方" align="center" prop="receiver" />
        <el-table-column label="业务唯一标识" align="center" prop="businessId" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:reportLog:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:reportLog:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改报文交互日志对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="报文方向" prop="direction">
                <el-input v-model="form.direction" type="textarea" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="报文描述" prop="msgDesc">
                <el-input v-model="form.msgDesc" placeholder="请输入报文描述" />
            </el-form-item>
            <el-form-item label="原始报文内容" prop="rawMessage">
                <el-input v-model="form.rawMessage" type="textarea" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="发送方" prop="sender">
                <el-input v-model="form.sender" placeholder="请输入发送方" />
            </el-form-item>
            <el-form-item label="接收方" prop="receiver">
                <el-input v-model="form.receiver" placeholder="请输入接收方" />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listReportLog,
    getReportLog,
    delReportLog,
    addReportLog,
    updateReportLog
} from "@/api/system/reportLog";

export default {
    name: "ReportLog",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 报文交互日志表格数据
            reportLogList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                createTime: null,
                direction: null,
                msgType: null,
                sender: null,
                receiver: null,
                businessId: null
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询报文交互日志列表 */
        getList() {
            this.loading = true;
            listReportLog(this.queryParams).then(response => {
                this.reportLogList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                logId: null,
                createTime: null,
                direction: null,
                msgType: null,
                msgDesc: null,
                rawMessage: null,
                sender: null,
                receiver: null,
                businessId: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.logId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加报文交互日志";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const logId = row.logId || this.ids
            getReportLog(logId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改报文交互日志";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.logId != null) {
                        updateReportLog(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addReportLog(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const logIds = row.logId || this.ids;
            this.$modal.confirm('是否确认删除报文交互日志编号为"' + logIds + '"的数据项？').then(function () {
                return delReportLog(logIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('system/reportLog/export', {
                ...this.queryParams
            }, `reportLog_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
