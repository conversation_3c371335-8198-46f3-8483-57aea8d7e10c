<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
        <el-form-item label="供应商名称" prop="supplierName">
            <el-input v-model="queryParams.supplierName" placeholder="请输入供应商名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="简称" prop="abbreviation">
            <el-input v-model="queryParams.abbreviation" placeholder="请输入简称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="供应商编码" prop="supplierCode">
            <el-input v-model="queryParams.supplierCode" placeholder="请输入供应商编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态">
                <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <!-- <el-form-item label="同步状态" prop="syncStatus">
            <el-select v-model="queryParams.syncStatus" placeholder="请选择状态" clearable>
                <el-option v-for="dict in dict.type.synchronization_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item> -->
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['basicInfo:supplier:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['basicInfo:supplier:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['basicInfo:supplier:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport" v-hasPermi="['basicInfo:supplier:import']">导入</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['basicInfo:supplier:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="supplierList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="供应商id" align="center" prop="supplierId" v-if="false" />
        <el-table-column label="供应商名称" align="center" prop="supplierName" />
        <el-table-column label="简称" align="center" prop="abbreviation" />
        <el-table-column label="供应商编码" align="center" prop="supplierCode" />
        <el-table-column label="社区名称" align="center" prop="communityId" v-if="false" />
        <el-table-column label="街道名称" align="center" prop="streetId" v-if="false" />
        <el-table-column label="部门名称" align="center" prop="deptId" v-if="false" />
        <el-table-column label="供应商级别" align="center" prop="supplierLevel" />
        <el-table-column label="邮箱" align="center" prop="mail" />
        <el-table-column label="供应商地址" align="center" prop="address" />
        <el-table-column label="联系人" align="center" prop="linkMan" />
        <el-table-column label="联系电话" align="center" prop="telephone" />
        <el-table-column label="启用状态" align="center" prop="status">
            <template slot-scope="scope">
                <el-switch v-model="scope.row.status" active-value="Y" inactive-value="N" @change="handleStatusChange(scope.row)"></el-switch>
            </template>
        </el-table-column>
        <!-- <el-table-column label="同步状态" align="center" prop="syncStatus">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.synchronization_status" :value="scope.row.syncStatus" />
            </template>
        </el-table-column> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['basicInfo:supplier:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['basicInfo:supplier:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改供应商对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="供应商名称" prop="supplierName">
                        <el-input v-model="form.supplierName" placeholder="请输入供应商名称" />
                    </el-form-item>
                    <el-form-item label="简称" prop="abbreviation">
                        <el-input v-model="form.abbreviation" placeholder="请输入简称" />
                    </el-form-item>
                    <el-form-item label="供应商编码" prop="supplierCode">
                        <el-input v-model="form.supplierCode" placeholder="请输入供应商编码" />
                    </el-form-item>
                    <el-form-item label="供应商级别" prop="supplierLevel">
                        <el-select v-model="form.supplierLevel" clearable placeholder="请选择供应商级别" style="width:100%">
                            <el-option v-for="dict in dict.type.supplier_level" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="邮箱" prop="mail">
                        <el-input v-model="form.mail" placeholder="请输入邮箱" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="供应商地址" prop="address">
                        <el-input v-model="form.address" placeholder="请输入供应商地址" />
                    </el-form-item>
                    <el-form-item label="联系人" prop="linkMan">
                        <el-input v-model="form.linkMan" placeholder="请输入联系人" />
                    </el-form-item>
                    <el-form-item label="联系电话" prop="telephone">
                        <el-input v-model="form.telephone" placeholder="请输入联系电话" />
                    </el-form-item>
                    <el-form-item label="启用状态" prop="status">
                        <el-select v-model="form.status" placeholder="请选择状态" style="width:100%">
                            <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <!-- <el-form-item label="同步状态" prop="syncStatus">
                        <el-select v-model="form.syncStatus" placeholder="请选择同步状态" style="width:100%">
                            <el-option v-for="dict in dict.type.synchronization_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item> -->
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>

    <!-- 供应商导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
        <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip text-center" slot="tip">
                <div class="el-upload__tip" slot="tip">
                    <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的供应商数据
                </div>
                <span>仅允许导入xls、xlsx格式文件。</span>
                <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
            </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listSupplier,
    getSupplier,
    delSupplier,
    addSupplier,
    updateSupplier
} from "@/api/basicInfo/supplier";
import { getToken } from "@/utils/auth";

export default {
    name: "Supplier",
    dicts: ['sys_yes_no', 'supplier_level', 'synchronization_status'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 供应商表格数据
            supplierList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                supplierName: null,
                abbreviation: null,
                supplierCode: null,
                communityId: null,
                streetId: null,
                deptId: null,
                status: 'Y',
                syncStatus: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                supplierName: [{
                    required: true,
                    message: "供应商名称不能为空",
                    trigger: "blur"
                }],
                supplierCode: [{
                    required: true,
                    message: "供应商编码不能为空",
                    trigger: "blur"
                }],
                telephone: [
                    { required: false, message: "联系电话不能为空", trigger: "blur" },
                    { 
                        pattern: /^((1[3-9]\d{9})|(\d{3,4}-\d{7,8}))$/, 
                        message: "请输入正确的联系电话（手机号或座机号）", 
                        trigger: "blur"
                    }
                ],
                status: [
                    { required: true, message: "启用状态不能为空", trigger: "change" }
                ]
            },
            // 新增：提交按钮加载动画
            submitLoading: false,
            // 供应商导入参数
            upload: {
                open: false,
                title: "",
                isUploading: false,
                updateSupport: 0,
                headers: {
                    Authorization: "Bearer " + getToken()
                },
                url: process.env.VUE_APP_BASE_API + "/basicInfo/supplier/importData"
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询供应商列表 */
        getList() {
            this.loading = true;
            listSupplier(this.queryParams).then(response => {
                this.supplierList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                supplierId: null,
                supplierName: null,
                abbreviation: null,
                supplierCode: null,
                communityId: null,
                streetId: null,
                deptId: null,
                supplierLevel: null,
                mail: null,
                address: null,
                linkMan: null,
                telephone: null,
                status: 'Y',
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },

        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 供应商状态修改
        handleStatusChange(row) {
            let text = row.status === "Y" ? "启用" : "停用";
            this.$modal.confirm('确认要"' + text + '""' + row.supplierName + '"供应商吗？').then(() => {
                return updateSupplier(row);
            }).then(() => {
                this.$modal.msgSuccess(text + "成功");
            }).catch(() => {
                row.status = row.status === "Y" ? "N" : "Y";
            });
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.supplierId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        async handleAdd() {
            this.reset();
            const res = await this.$getAutoCode()
            this.form.supplierCode = res.msg
            this.open = true;
            this.form.status = 'Y';
            this.title = "添加供应商";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const supplierId = row.supplierId || this.ids
            getSupplier(supplierId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改供应商";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.submitLoading = true;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.supplierId != null) {
                        updateSupplier(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    } else {
                        addSupplier(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    }
                } else {
                    this.submitLoading = false;
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const supplierIds = row.supplierId || this.ids;
            this.$modal.confirm('是否确认删除供应商编号为"' + supplierIds + '"的数据项？').then(function () {
                return delSupplier(supplierIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('basicInfo/supplier/export', {
                ...this.queryParams
            }, `supplier_${new Date().getTime()}.xlsx`)
        },
        /** 导入按钮操作 */
        handleImport() {
            this.upload.title = "供应商导入";
            this.upload.open = true;
        },
        /** 下载模板操作 */
        importTemplate() {
            this.download('basicInfo/supplier/importTemplate', {}, `supplier_template_${new Date().getTime()}.xlsx`)
        },
        // 文件上传中处理
        handleFileUploadProgress(event, file, fileList) {
            this.upload.isUploading = true;
        },
        // 文件上传成功处理
        handleFileSuccess(response, file, fileList) {
            this.upload.open = false;
            this.upload.isUploading = false;
            this.$refs.upload.clearFiles();
            this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {
                dangerouslyUseHTMLString: true
            });
            this.getList();
        },
        // 提交上传文件
        submitFileForm() {
            this.$refs.upload.submit();
        },
    }
};
</script>
