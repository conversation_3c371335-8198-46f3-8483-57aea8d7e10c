<template>
  <el-dialog
    :visible.sync="visible"
    width="800px"
    title="仪表通讯设置"
    :close-on-click-modal="false"
    class="instrument-dialog"
  >
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="1#仪表" name="tab1" />
      <el-tab-pane label="2#仪表" name="tab2" />
    </el-tabs>
    <div class="instrument-content">
      <div class="instrument-left">
        <el-form :model="form" label-width="90px" size="mini">
          <el-divider content-position="left">串口参数</el-divider>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="端口">
                <el-select v-model="form.port" style="width: 100%">
                  <el-option label="COM1" value="COM1" />
                  <el-option label="COM2" value="COM2" />
                  <el-option label="COM3" value="COM3" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="波特率">
                <el-select v-model="form.baudRate" style="width: 100%">
                  <el-option label="4800" value="4800" />
                  <el-option label="9600" value="9600" />
                  <el-option label="115200" value="115200" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="数据位">
                <el-select v-model="form.dataBits" style="width: 100%">
                  <el-option label="8" value="8" />
                  <el-option label="7" value="7" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="停止位">
                <el-select v-model="form.stopBits" style="width: 100%">
                  <el-option label="1" value="1" />
                  <el-option label="2" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="奇偶校验">
                <el-select v-model="form.parity" style="width: 100%">
                  <el-option label="无校验" value="none" />
                  <el-option label="奇校验" value="odd" />
                  <el-option label="偶校验" value="even" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="检测间隔">
                <el-input v-model="form.checkInterval" type="number" suffix="ms" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">仪表型号</el-divider>
          <el-form-item label="仪表型号">
            <el-select v-model="form.model" style="width: 100%">
              <el-option label="1.上海耀华 XK3190-A9" value="1" />
              <el-option label="2.其他型号" value="2" />
            </el-select>
          </el-form-item>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="仪表计量单位">
                <el-select v-model="form.unit" style="width: 100%">
                  <el-option label="公斤" value="公斤" />
                  <el-option label="吨" value="吨" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="软件计量单位">
                <el-select v-model="form.softwareUnit" style="width: 100%">
                  <el-option label="公斤" value="公斤" />
                  <el-option label="吨" value="吨" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="仪表显示格式">
                <el-select v-model="form.displayFormat" style="width: 100%">
                  <el-option label="0" value="0" />
                  <el-option label="1" value="1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="仪表小数位">
                <el-select v-model="form.decimal" style="width: 100%">
                  <el-option label="1" value="1" />
                  <el-option label="2" value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">自定义仪表参数</el-divider>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="ASCII码">
                <el-input v-model="form.ascii" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="起始位">
                <el-input v-model="form.startBit" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="截取位">
                <el-input v-model="form.cutBit" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="数据长度">
                <el-input v-model="form.dataLength" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="小数点">
                <el-input v-model="form.decimalPoint" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="顺序">
                <el-input v-model="form.order" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider content-position="left">远程仪表</el-divider>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-checkbox v-model="form.allowRead">允许远程读取</el-checkbox>
            </el-col>
            <el-col :span="8">
              <el-checkbox v-model="form.remoteRead">从远程读取</el-checkbox>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="IP地址">
                <el-input v-model="form.ip" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="端口号">
                <el-input v-model="form.port1" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="本机端口">
                <el-input v-model="form.port2" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-divider content-position="left">同步输出到大屏幕</el-divider>
        <el-row :gutter="10">
          <el-col :span="16">
            <el-form-item label="输出当前重量">
              <el-select v-model="form.screenOutput" style="width: 100%">
                <el-option label="输出当前重量" value="current" />
                <el-option label="输出其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-radio-group v-model="form.outputType">
              <el-radio label="持续输出">持续输出</el-radio>
              <el-radio label="保存后输出">保存后输出</el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
      </div>
      <div class="instrument-right">
        <el-form label-width="0">
          <el-form-item>
            <el-input
              type="textarea"
              v-model="form.dataDisplay"
              :rows="15"
              style="width: 100%; margin-bottom: 10px;"
              readonly
            />
          </el-form-item>
        </el-form>
        <div style="margin-bottom: 10px;">
          <span>数据显示模式：</span>
          <el-radio-group v-model="form.displayMode" size="mini">
            <el-radio label="decimal">十进制</el-radio>
            <el-radio label="hex">十六进制</el-radio>
          </el-radio-group>
          <el-button size="mini" @click="onClear" style="margin-left: 10px;">清空</el-button>
        </div>
        <el-form label-width="0">
          <el-form-item>
            <el-input v-model="form.sendData" placeholder="" style="width: 100%; margin-bottom: 10px;" />
          </el-form-item>
        </el-form>
        <el-checkbox v-model="form.autoSend" style="margin-right: 10px;">自动发送</el-checkbox>
        <el-button size="mini" type="primary" @click="onSend">发送</el-button>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onCancel">取消</el-button>
      <el-button type="primary" @click="onConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'InstrumentConfig',
  props: {
    visible: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      activeTab: 'tab1',
      form: {
        port: 'COM1',
        baudRate: '4800',
        dataBits: '8',
        stopBits: '1',
        parity: 'none',
        checkInterval: 500,
        model: '1',
        unit: '公斤',
        softwareUnit: '公斤',
        displayFormat: '0',
        decimal: '1',
        ascii: '',
        startBit: '',
        cutBit: '1',
        dataLength: '7',
        decimalPoint: '0',
        order: '正序',
        allowRead: false,
        remoteRead: false,
        ip: '127.0.0.1',
        port1: '8888',
        port2: '8888',
        screenOutput: 'current',
        outputType: '持续输出',
        dataDisplay: '',
        displayMode: 'decimal',
        sendData: '',
        autoSend: false
      }
    }
  },
  methods: {
    onClear() {
      this.form.dataDisplay = ''
    },
    onSend() {
      // TODO: 调用后端接口发送数据
      // 示例: this.$emit('send', this.form.sendData)
    },
    onConfirm() {
      // TODO: 调用后端接口保存配置
      // 示例: this.$emit('confirm', this.form)
      this.$emit('update:visible', false)
    },
    onCancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
.instrument-dialog >>> .el-dialog__body {
  padding-top: 0;
}
.instrument-content {
  display: flex;
  flex-direction: row;
  margin-top: 10px;
}
.instrument-left {
  width: 60%;
  padding-right: 10px;
  border-right: 1px solid #eee;
}
.instrument-right {
  width: 40%;
  padding-left: 10px;
}
.dialog-footer {
  text-align: right;
}
</style>
