import { toUnicode } from "@/utils/chToUnicode";
let socket;
let timer;
//心跳机制
let heart = {
    timeObj: null,
    serverTimeObj: null,
    start: function(url, callback, timeOut,dbCode) {
        let self = this;
        //清除延时器
        this.timeObj && clearTimeout(this.timeObj);
        this.serverTimeObj && clearTimeout(this.serverTimeObj);
        this.timeObj = setTimeout(function() {
            //连接状态
            console.log("socket connect: " + socket.readyState);

            //获取当前时间戳
            // let datetime = Date.now()
            // let info = {
            //   data: datetime,
            //   command: "heartbeat"
            // };
            // socket.send(JSON.stringify(info));

            //发送消息，服务端返回信息，即表示连接良好，可以在socket的onmessage事件重置心跳机制函数
            //定义一个延时器等待服务器响应，若超时，则关闭连接，重新请求server建立socket连接
            self.serverTimeObj = setTimeout(function() {
                if (socket.readyState != 1) {
                    socket.close();
                    socket = null;
                    openSocket(url, callback, timeOut, dbCode);
                }
            }, timeOut);
        }, timeOut);
    }
};

export function openSocket(url, callback, timeOut,dbCode) {
    let dbCodes = window.localStorage.getItem('dbCodeIfo') || dbCode 
    if (typeof WebSocket == "undefined") {
        console.log("您的浏览器不支持WebSocket");
        return;
    }
    console.log("您的浏览器支持WebSocket");
    url = url.replace("https", "wss").replace("http", "ws");
    console.log(url);
    if (socket != null) {
        socket.close();
        socket = null;
    }
    socket = new WebSocket(url);
    //打开事件
    socket.onopen = () => {
        console.log("websocket已打开");
        window.sessionStorage.setItem("socketConnect", socket.readyState)
        heart.start(url, callback, timeOut);
        // let data = JSON.stringify({"dbCode":dbCodes,"typeCode":"connect",})
        // socket.send(data)
    };
    //获得消息事件
    socket.onmessage = msg => {
        callback(msg);
        heart.start(url, callback, timeOut,dbCodes);
    };
    //关闭事件
    socket.onclose = () => {
        console.log("websocket已关闭");
        window.sessionStorage.setItem("socketConnect", socket.readyState)
        // openSocket(url, callback, timeOut,dbCode);
    };
    //发生了错误事件
    socket.onerror = () => {
        console.log("websocket发生了错误");
        clearTimeout(timer)
        timer = null
        timer = setTimeout(() => {
            debounce(url, callback, timeOut)
        }, 3000)
        // openSocket(url, callback, timeOut,dbCode);
    };
}

export function sendMsg(msg) {
    let msgStr = JSON.stringify(msg);
    msgStr = toUnicode(msgStr);
    socket.send(msgStr);
}

export function endSocket() {
    clearTimeout(timer)
    timer = null
    socket.close();
    socket = null;
    console.log("websocket称重关闭");
}

function debounce(url, callback, timeOut) {
    openSocket(url, callback, timeOut)
    clearTimeout(timer)
    timer = null
}