<template>
  <div>
    <div ref="printTemplate" style="display:none"></div>
    <button @click="print">打印磅单</button>
  </div>
</template>

<script>
export default {
  name: 'PoundPrint',
  props: {
    printData: { type: Object, required: true }, // 传入的磅单数据
    templateConfig: { type: Object, required: false, default: null } // 可选：自定义模板
  },
  mounted() {
    this.initPrinter()
  },
  methods: {
    initPrinter() {
      // 默认模板
      const defaultTemplate = {
        panels: [
          {
            index: 0,
            width: 800,
            height: 600,
            paperType: 'A4',
            paperHeader: 0,
            paperFooter: 0,
            printElements: [
              {
                options: { left: 50, top: 30, width: 200, height: 30, field: 'title', title: '磅单', fontSize: 20, textAlign: 'center' },
                printElementType: { title: '文本', type: 'text' }
              },
              {
                options: { left: 50, top: 80, width: 300, height: 30, field: 'carNo', title: '车牌号' },
                printElementType: { title: '文本', type: 'text' }
              },
              {
                options: { left: 50, top: 120, width: 300, height: 30, field: 'weight', title: '重量' },
                printElementType: { title: '文本', type: 'text' }
              },
              // ... 你可以继续添加更多字段
            ]
          }
        ]
      }
      this.template = this.templateConfig || defaultTemplate
    },
    print() {
      // 渲染模板
      this.$hiprintTemplate(this.template, this.$refs.printTemplate)
        .print(this.printData)
    }
  }
}
</script>
