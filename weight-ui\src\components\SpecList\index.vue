<template>
  <div class="spec-list" :class="{ 'has-material': currentMaterial }">
    <!-- 物料信息展示区域 -->
    <div v-if="currentMaterial" class="material-info-bar">
      <div class="material-info">
        <i class="el-icon-box"></i>
        <span class="material-name">{{ currentMaterial.materialName }}</span>
        <span class="material-code">{{ currentMaterial.materialCode }}</span>
      </div>
      <div class="spec-count">
        规格数量: {{ total }}
      </div>
    </div>
    
    <el-card shadow="always" class="main-card">
      <div slot="header" class="card-header">
        <span class="card-title">规格管理</span>
        <div class="btn-group">
          <el-button type="primary" icon="el-icon-plus" size="mini" v-hasPermi="['basicInfo:spec:add']" :disabled="!currentMaterial" @click="openDialog('add')">新增</el-button>
          <el-button type="danger" icon="el-icon-delete" size="mini" v-hasPermi="['basicInfo:spec:edit']" :disabled="!selection.length" @click="handleBatchDelete">批量删除</el-button>
          <el-button type="info" icon="el-icon-upload2" size="mini" v-hasPermi="['basicInfo:spec:remove']" :disabled="!currentMaterial" @click="handleImport">导入</el-button>
          <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
        </div>
      </div>
      
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="search-form" label-width="68px">
        <el-form-item label="规格编号" prop="specCode">
          <el-input v-model="queryParams.specCode" placeholder="请输入规格编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="规格名称" prop="specName">
          <el-input v-model="queryParams.specName" placeholder="请输入规格名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态">
            <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 空状态 -->
      <div v-if="!currentMaterial" class="empty-state">
        <div class="empty-icon">
          <i class="el-icon-box"></i>
        </div>
        <div class="empty-text">请选择物料查看对应规格</div>
        <div class="empty-hint">在左侧物料列表中点击选择物料</div>
      </div>
      
      <!-- 表格 -->
      <el-table 
        v-else
        :data="list" 
        highlight-current-row 
        @current-change="handleRowClick" 
        :loading="loading" 
        :row-key="row => row.specId || row.id || row.specCode" 
        ref="table" 
        class="main-table" 
        :header-cell-style="headerCellStyle" 
        :cell-style="cellStyle" 
        empty-text="暂无规格数据" 
        @selection-change="handleSelectionChange" 
        height="calc(100% - 120px)"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column prop="specCode" label="规格编号" min-width="100" />
        <el-table-column prop="specName" label="规格名称" min-width="100" />
        <el-table-column prop="status" label="启用状态" min-width="80">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" active-value="Y" inactive-value="N" @change="handleStatusChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120" align="center" :key="'spec-operation'">
          <template slot-scope="scope">
            <el-button type="text" size="mini" v-hasPermi="['basicInfo:spec:edit']" @click="openDialog('edit', scope.row)">编辑</el-button>
            <el-button type="text" size="mini" v-hasPermi="['basicInfo:spec:remove']" style="color:#F56C6C" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 分页 -->
    <pagination 
      v-show="total > 0" 
      :total="total" 
      :page.sync="queryParams.pageNum" 
      :limit.sync="queryParams.pageSize" 
      @pagination="getList" 
      class="card-pagination" 
    />

    <!-- 编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="420px" :close-on-click-modal="false" class="custom-dialog">
      <el-form :model="form" :rules="rules" ref="form" label-width="90px">
        <el-form-item label="规格编号" prop="specCode">
          <el-input v-model="form.specCode" placeholder="请输入规格编号" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="规格名称" prop="specName">
          <el-input v-model="form.specName" placeholder="请输入规格名称" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%;">
            <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload 
        ref="upload" 
        :limit="1" 
        accept=".xlsx, .xls" 
        :headers="upload.headers" 
        :action="upload.url + '?updateSupport=' + upload.updateSupport + '&materialId=' + (currentMaterial ? currentMaterial.materialId : '')" 
        :disabled="upload.isUploading" 
        :on-progress="handleFileUploadProgress(upload)" 
        :on-success="handleFileUploadSuccess(upload, 'upload', getList)" 
        :auto-upload="false" 
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的规格数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="handleDownloadTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFile('upload')">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSpec,
  getSpec,
  addSpec,
  updateSpec,
  delSpec
} from "@/api/basicInfo/spec";
import tableCommon from "@/mixins/tableCommon";
import importCommon from "@/mixins/importCommon";

export default {
  name: "SpecList",
  dicts: ['sys_yes_no'],
  mixins: [tableCommon, importCommon],
  props: {
    currentMaterial: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      currentRow: null,
      dialogVisible: false,
      dialogTitle: "",
      dialogType: "add",
      dialogLoading: false,
      form: {
        specCode: "",
        specName: "",
        status: 'Y',
      },
      rules: {
        specCode: [{
          required: true,
          message: "规格编号不能为空",
          trigger: "blur"
        }],
        specName: [{
          required: true,
          message: "规格名称不能为空",
          trigger: "blur"
        }],
        status: [{
          required: true,
          message: "请选择启用状态",
          trigger: "change"
        }]
      },
      selection: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        specCode: '',
        specName: '',
        status: 'Y',
      },
      total: 0,
      upload: this.createImportConfig("/basicInfo/spec/importData")
    };
  },
  watch: {
    currentMaterial: {
      handler(newVal) {
        if (newVal) {
          this.getList();
        } else {
          this.list = [];
          this.total = 0;
          this.currentRow = null;
        }
      },
      immediate: true
    }
  },
  methods: {
    // 规格状态修改
    handleStatusChange(row) {
      let text = row.status === "Y" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.specName + '"规格吗？').then(() => {
        return updateSpec(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {
        row.status = row.status === "Y" ? "N" : "Y";
      });
    },
    
    // 获取规格列表
    getList() {
      if (!this.currentMaterial) {
        this.list = [];
        this.total = 0;
        return;
      }
      
      this.loading = true;
      const params = {
        ...this.queryParams,
        materialId: this.currentMaterial.materialId
      };
      
      listSpec(params).then(res => {
        this.list = res.rows;
        this.total = res.total;
        this.loading = false;
        
        // 自动选中第一行
        if (this.list.length) {
          this.$nextTick(() => {
            this.$refs.table.setCurrentRow(this.list[0]);
            this.handleRowClick(this.list[0]);
          });
        } else {
          this.currentRow = null;
        }
      });
    },
    
    // 行点击事件
    handleRowClick(row) {
      this.currentRow = row;
      this.$emit('spec-change', row);
    },
    
    // 打开对话框
    async openDialog(type, row) {
      if (!this.currentMaterial) {
        this.$message.warning("请先选择物料");
        return;
      }
      
      this.dialogType = type;
      if (type === "add") {
        const res = await this.$getAutoCode();
        this.dialogTitle = "新增规格";
        this.form = {
          specCode: res.msg,
          specName: "",
          status: 'Y',
        };
      } else if (type === "edit") {
        const target = row || this.currentRow;
        if (!target) return;
        this.dialogTitle = "编辑规格";
        this.form = { ...target };
      }
      this.dialogVisible = true;
    },
    
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (!valid) return;
        this.dialogLoading = true;
        
        const data = {
          ...this.form,
          materialId: this.currentMaterial.materialId
        };
        
        const api = this.dialogType === "add" ? addSpec : updateSpec;
        api(data).then(() => {
          this.$message.success(this.dialogType === "add" ? "新增成功" : "修改成功");
          this.dialogVisible = false;
          this.getList();
        }).finally(() => {
          this.dialogLoading = false;
        });
      });
    },
    
    // 删除
    handleDelete(row) {
      const target = row || this.currentRow;
      if (!target) return;
      this.confirmDelete(target.specName, () => {
        return delSpec(target.specId).then(() => {
          this.$message.success("删除成功");
          this.getList();
        });
      });
    },
    
    // 批量删除
    handleBatchDelete() {
      if (!this.selection.length) return;
      this.confirmBatchDelete(() => {
        return delSpec(this.selection.map(i => i.specId)).then(() => {
          this.$message.success("批量删除成功");
          this.getList();
        });
      });
    },
    
    // 选择变化
    handleSelectionChange(val) {
      this.selection = val;
    },
    
    // 导出
    handleExport() {
      if (!this.currentMaterial) return;
      this.download && this.download('basicInfo/spec/export', {
        ...this.queryParams,
        materialId: this.currentMaterial.materialId
      }, `spec_${new Date().getTime()}.xlsx`);
    },
    
    // 导入
    handleImport() {
      if (!this.currentMaterial) {
        this.$message.warning("请先选择物料");
        return;
      }
      this.upload.title = "规格导入";
      this.upload.open = true;
    },
    
    // 下载模板
    handleDownloadTemplate() {
      if (!this.currentMaterial) {
        this.$message.warning("请先选择物料");
        return;
      }
      this.downloadTemplate('basicInfo/spec/importTemplate', { 
        materialId: this.currentMaterial.materialId 
      }, `spec_template_${new Date().getTime()}.xlsx`);
    },
    
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    // 重置
    resetQuery() {
      this.resetQueryParams('queryForm', this.queryParams);
      this.getList();
    }
  }
};
</script>

<style scoped>
.spec-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 物料信息展示区域 */
.material-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #409eff 0%, #409eff 100%);
  color: white;
  padding: 12px 16px;
  border-radius: 6px 6px 0 0;
  font-size: 14px;
  margin-bottom: 0;
}

.material-info {
  display: flex;
  align-items: center;
}

.material-info .el-icon-box {
  font-size: 16px;
  margin-right: 8px;
}

.material-name {
  font-weight: 600;
  margin-right: 12px;
}

.material-code {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.spec-count {
  font-size: 12px;
  opacity: 0.9;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
}

.empty-icon {
  font-size: 64px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 12px;
  color: #909399;
}

.main-card {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  border-radius: 0 0 6px 6px;
  box-shadow: none;
  border: none;
  padding: 16px 10px 16px 10px;
  margin: 0;
  height: 100%;
  overflow: hidden;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: 50px;
}

/* 当没有选中物料时，恢复卡片样式 */
.spec-list:not(.has-material) .main-card {
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header,
.search-form,
.btn-group {
  flex-shrink: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  margin-bottom: 12px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #222;
}

.sub-title {
  font-size: 15px;
  color: #409EFF;
  margin-left: 6px;
}

.btn-group {
  display: flex;
  align-items: center;
  margin-left: auto;
  justify-content: flex-end;
}

.btn-group .el-button {
  margin-left: 6px;
}

.search-form {
  margin-bottom: 12px;
  width: 100%;
}

.main-table {
  flex: 1 1 0;
  min-height: 0;
  border-radius: 8px;
  background: #fff;
  margin-top: 0;
}

::v-deep .el-table {
  height: 70vh !important;
  display: flex;
  flex-direction: column;
}

.el-table::before {
  height: 0;
}

::v-deep .el-table__header-wrapper,
::v-deep .el-table__footer-wrapper {
  flex-shrink: 0;
}

::v-deep .el-table__body-wrapper {
  min-height: 0;
  height: calc(100% - 140px);
  overflow-y: auto !important;
  max-height: 100% !important;
}

.custom-dialog>>>.el-dialog__header {
  border-radius: 10px 10px 0 0;
  background: #f5f7fa;
  padding: 16px 20px;
}

.custom-dialog>>>.el-dialog__title {
  font-size: 16px;
  font-weight: bold;
}

.custom-dialog>>>.el-dialog__body {
  padding-top: 18px;
  padding-bottom: 0;
}

.dialog-footer {
  text-align: right;
  padding: 10px 0 0 0;
}

.card-pagination {
  margin: 12px auto 0 auto;
  text-align: center;
  width: 100%;
  position: static;
  padding: 0;
  background: none;
  box-shadow: none;
  z-index: auto;
}
</style>