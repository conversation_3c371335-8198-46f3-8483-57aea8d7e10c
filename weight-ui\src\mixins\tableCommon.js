/**
 * 表格通用混入
 */
export default {
  methods: {
    // 表格样式
    headerCellStyle() {
      return {
        background: '#f5f7fa',
        fontWeight: 'bold',
        color: '#333',
        fontSize: '15px'
      };
    },
    cellStyle() {
      return {
        fontSize: '15px',
        color: '#444'
      };
    },
    
    // 通用搜索重置
    resetQueryParams(queryForm, queryParams) {
      this.$refs[queryForm].resetFields();
      queryParams.pageNum = 1;
      queryParams.pageSize = 10;
    },
    
    // 通用确认删除
    confirmDelete(name, callback) {
      return this.$confirm(`确定要删除"${name}"吗？`, "提示", {
        type: "warning"
      }).then(callback);
    },
    
    // 通用批量删除确认
    confirmBatchDelete(callback) {
      return this.$confirm(`确定要批量删除选中的数据吗？`, "提示", {
        type: "warning"
      }).then(callback);
    }
  }
};