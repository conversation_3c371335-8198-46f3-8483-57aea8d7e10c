// 把字符串中的汉字转换成Unicode
export function toUnicode(str) {
  if (!str) {
    return;
  }
  var unicode = '';
  for (var i = 0; i < str.length; i++) {
    var temp = str.charAt(i);
    if (isChinese(temp)) {
      unicode += '\\u' + temp.charCodeAt(0).toString(16);
    } else {
      unicode += temp;
    }
  }
  return unicode;
}


// 判断字符是否为汉字，
export function isChinese(s) {
  return /[\u4e00-\u9fa5]/.test(s);
}
