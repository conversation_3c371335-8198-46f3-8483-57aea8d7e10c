<template>
<div>
    <el-dialog title="过 磅 图 片" :visible.sync="open" :close-on-click-modal="false" width="65%" class="img-eel-dialog" append-to-body :before-close="handleCloseImg">
        <div class="img">
            <ul>
                <li v-for="item in arr" :key="item.id">
                    <img class="img-box" @click="img_click(item.url)" :src="item.url" />
                </li>
            </ul>
        </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible" width="65%" :before-close="handleClose" append-to-body>
        <div>
            <img style="width:100%" :src="dialogVisible_img" alt="" />
        </div>
    </el-dialog>
</div>
</template>

<script>
export default {
    name: "TcImg",
    data() {
        return {
            dialogVisible_img: '',
            dialogVisible: false,
            open: false,
            arr: [],
        };
    },

    methods: {
        handleClose() {
            this.dialogVisible = false
        },
        img_click(url) {
            this.dialogVisible_img = url
            this.dialogVisible = true
        },
        //开启弹窗
        openDialog(imgArr, httpUrl) {
            // 去除结尾斜杠，保证拼接时只有一个斜杠
            this.arr = imgArr.map((img, index) => ({
                id: index,
                name: `00${index}`,
                url: img ? `http://${httpUrl}/weight/weight/getImg?imgUrl=${String(img).replace(/\\/g, '/')}`: undefined,
            }));
            console.log(this.arr);

            // 如果图片数量是奇数，则添加一个空图片占位
            if (imgArr.length % 2 !== 0) {
                this.arr.push({
                    id: imgArr.length,
                    name: `00${imgArr.length}`,
                    url: undefined,
                });
            }

            this.open = true;
        },

        cancel() {
            this.open = false;
        },
        handleCloseImg() {
            this.$showPlugin()
            this.open = false;
            this.arr = []
        }
    },
};
</script>

<style scoped>
.img-eel-dialog>>>.el-dialog {
    margin-top: 0 !important;
}

.img-eel-dialog>>>.el-dialog__header {
    background-color: #1d6498;
    padding: 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.img-eel-dialog>>>.el-dialog__title {
    color: #f3f4f5;
    font-size: 2.5rem;
    font-weight: 900;
}

.img-eel-dialog>>>.el-dialog__headerbtn {
    width: 2.5rem;
    height: 2.5rem;
    top: 10px;
}

.img-eel-dialog>>>.el-dialog__headerbtn .el-dialog__close {
    line-height: 3.4rem;
    font-size: 2.5rem;
    color: #f3f4f5;
}

.el-dialog__body {
    padding: 15px 20px;
}

.dialog-footer {
    width: 100%;
    height: 1px;
    display: flex;
    justify-content: center;
}

.img {
    width: 100%;
    /* height: 50%; */
    border-radius: 0.05rem;
    padding: 0.05rem;
    /* background-color: #4791c7; */
    z-index: 100;
    overflow: hidden;
}

.img>>>img {
    width: 99.2%;
    height: 100%;
}

ul {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

ul li {
    width: 50%;
    height: 50%;
    list-style: none;
    padding: 0.03rem;
}
</style>
