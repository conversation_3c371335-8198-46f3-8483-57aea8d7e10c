/**
$base-menu-color:hsla(0,0%,100%,.65);
$base-menu-color-active:#fff;
$base-menu-background:#001529;
$base-logo-title-color: #ffffff;

$base-menu-light-color:rgba(0,0,0,.70);
$base-menu-light-background:#ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background:#000c17;
$base-sub-menu-hover:#001528;
*/
:export {
  menuColor: #fff;
  menuLightColor: rgba(0, 0, 0, 0.7);
  menuColorActive: #fff;
  menuBackground: #0094d9;
  menuLightBackground: #ffffff;
  subMenuBackground: #008cba;
  subMenuHover: #156aa8;
  sideBarWidth: 200px;
  logoTitleColor: #ffffff;
  logoLightTitleColor: #001529;
}

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.28s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

/* fade-transform */
.fade-transform--move,
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all 0.5s;
}

.breadcrumb-leave-active {
  position: absolute;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload input[type=file] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.cell .el-tag {
  margin-right: 0px;
}

.small-padding .cell {
  padding-left: 5px;
  padding-right: 5px;
}

.fixed-width .el-button--mini {
  padding: 7px 10px;
  width: 60px;
}

.status-col .cell {
  padding: 0 10px;
  text-align: center;
}
.status-col .cell .el-tag {
  margin-right: 0px;
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container .el-upload {
  width: 100%;
}
.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-dropdown-menu a {
  display: block;
}

.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

#app .main-container {
  height: 100%;
  transition: margin-left 0.28s;
  position: relative;
}
#app .sidebarHide {
  margin-left: 0 !important;
}
#app .sidebar-container {
  transition: width 0.28s;
  width: 200px !important;
  background-color: #0094d9;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.1);
}
#app .sidebar-container .horizontal-collapse-transition {
  transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
}
#app .sidebar-container .scrollbar-wrapper {
  overflow-x: hidden !important;
}
#app .sidebar-container .el-scrollbar__bar.is-vertical {
  right: 0px;
}
#app .sidebar-container .el-scrollbar {
  height: 100%;
}
#app .sidebar-container.has-logo .el-scrollbar {
  height: calc(100% - 50px);
}
#app .sidebar-container .is-horizontal {
  display: none;
}
#app .sidebar-container a {
  display: inline-block;
  width: 100%;
  overflow: hidden;
}
#app .sidebar-container .svg-icon {
  margin-right: 16px;
}
#app .sidebar-container .el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}
#app .sidebar-container .el-menu-item, #app .sidebar-container .el-submenu__title {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}
#app .sidebar-container .submenu-title-noDropdown:hover,
#app .sidebar-container .el-submenu__title:hover {
  background-color: rgba(0, 0, 0, 0.06) !important;
}
#app .sidebar-container .theme-dark .is-active > .el-submenu__title {
  color: #fff !important;
}
#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title, #app .sidebar-container .el-submenu .el-menu-item {
  min-width: 200px !important;
}
#app .sidebar-container .nest-menu .el-submenu > .el-submenu__title:hover, #app .sidebar-container .el-submenu .el-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.06) !important;
}
#app .sidebar-container .theme-dark .nest-menu .el-submenu > .el-submenu__title, #app .sidebar-container .theme-dark .el-submenu .el-menu-item {
  background-color: #008cba !important;
}
#app .sidebar-container .theme-dark .nest-menu .el-submenu > .el-submenu__title:hover, #app .sidebar-container .theme-dark .el-submenu .el-menu-item:hover {
  background-color: #156aa8 !important;
}
#app .hideSidebar .sidebar-container {
  width: 54px !important;
}
#app .hideSidebar .submenu-title-noDropdown .el-tooltip .svg-icon {
  margin-left: 20px;
}
#app .hideSidebar .el-submenu {
  overflow: hidden;
}
#app .hideSidebar .el-submenu > .el-submenu__title {
  padding: 0 !important;
}
#app .hideSidebar .el-submenu > .el-submenu__title .svg-icon {
  margin-left: 20px;
}
#app .hideSidebar .el-menu--collapse .el-submenu > .el-submenu__title > span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}
#app .el-menu--collapse .el-menu .el-submenu {
  min-width: 200px !important;
}
#app .mobile .main-container {
  margin-left: 0px;
}
#app .mobile .sidebar-container {
  transition: transform 0.28s;
  width: 200px !important;
}
#app .mobile.hideSidebar .sidebar-container {
  pointer-events: none;
  transition-duration: 0.3s;
  transform: translate3d(-200px, 0, 0);
}
#app .withoutAnimation .main-container,
#app .withoutAnimation .sidebar-container {
  transition: none;
}

.el-menu--vertical > .el-menu .svg-icon {
  margin-right: 16px;
}
.el-menu--vertical .nest-menu .el-submenu > .el-submenu__title:hover,
.el-menu--vertical .el-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.06) !important;
}
.el-menu--vertical > .el-menu--popup {
  max-height: 100vh;
  overflow-y: auto;
}
.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-track-piece {
  background: #d3dce6;
}
.el-menu--vertical > .el-menu--popup::-webkit-scrollbar {
  width: 6px;
}
.el-menu--vertical > .el-menu--popup::-webkit-scrollbar-thumb {
  background: #99a9bf;
  border-radius: 20px;
}

/**
$base-menu-color:hsla(0,0%,100%,.65);
$base-menu-color-active:#fff;
$base-menu-background:#001529;
$base-logo-title-color: #ffffff;

$base-menu-light-color:rgba(0,0,0,.70);
$base-menu-light-background:#ffffff;
$base-logo-light-title-color: #001529;

$base-sub-menu-background:#000c17;
$base-sub-menu-hover:#001528;
*/
:export {
  menuColor: #fff;
  menuLightColor: rgba(0, 0, 0, 0.7);
  menuColorActive: #fff;
  menuBackground: #0094d9;
  menuLightBackground: #ffffff;
  subMenuBackground: #008cba;
  subMenuHover: #156aa8;
  sideBarWidth: 200px;
  logoTitleColor: #ffffff;
  logoLightTitleColor: #001529;
}

.blue-btn {
  background: #324157;
}
.blue-btn:hover {
  color: #324157;
}
.blue-btn:hover:before, .blue-btn:hover:after {
  background: #324157;
}

.light-blue-btn {
  background: #3A71A8;
}
.light-blue-btn:hover {
  color: #3A71A8;
}
.light-blue-btn:hover:before, .light-blue-btn:hover:after {
  background: #3A71A8;
}

.red-btn {
  background: #C03639;
}
.red-btn:hover {
  color: #C03639;
}
.red-btn:hover:before, .red-btn:hover:after {
  background: #C03639;
}

.pink-btn {
  background: #E65D6E;
}
.pink-btn:hover {
  color: #E65D6E;
}
.pink-btn:hover:before, .pink-btn:hover:after {
  background: #E65D6E;
}

.green-btn {
  background: #30B08F;
}
.green-btn:hover {
  color: #30B08F;
}
.green-btn:hover:before, .green-btn:hover:after {
  background: #30B08F;
}

.tiffany-btn {
  background: #4AB7BD;
}
.tiffany-btn:hover {
  color: #4AB7BD;
}
.tiffany-btn:hover:before, .tiffany-btn:hover:after {
  background: #4AB7BD;
}

.yellow-btn {
  background: #FEC171;
}
.yellow-btn:hover {
  color: #FEC171;
}
.yellow-btn:hover:before, .yellow-btn:hover:after {
  background: #FEC171;
}

.pan-btn {
  font-size: 14px;
  color: #fff;
  padding: 14px 36px;
  border-radius: 8px;
  border: none;
  outline: none;
  transition: 600ms ease all;
  position: relative;
  display: inline-block;
}
.pan-btn:hover {
  background: #fff;
}
.pan-btn:hover:before, .pan-btn:hover:after {
  width: 100%;
  transition: 600ms ease all;
}
.pan-btn:before, .pan-btn:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  height: 2px;
  width: 0;
  transition: 400ms ease all;
}
.pan-btn::after {
  right: inherit;
  top: inherit;
  left: 0;
  bottom: 0;
}

.custom-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  color: #fff;
  -webkit-appearance: none;
  text-align: center;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  padding: 10px 15px;
  font-size: 14px;
  border-radius: 4px;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
aside a {
  color: #337ab7;
  cursor: pointer;
}
aside a:hover {
  color: rgb(32, 160, 255);
}

.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgb(32, 182, 249) 0%, rgb(32, 182, 249) 0%, rgb(33, 120, 241) 100%, rgb(33, 120, 241) 100%);
}
.sub-navbar .subtitle {
  font-size: 20px;
  color: #fff;
}
.sub-navbar.draft {
  background: #d0d0d0;
}
.sub-navbar.deleted {
  background: #d0d0d0;
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;
}
.link-type:hover,
.link-type:focus:hover {
  color: rgb(32, 160, 255);
}

.filter-container {
  padding-bottom: 10px;
}
.filter-container .filter-item {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 10px;
}/*# sourceMappingURL=index.css.map */