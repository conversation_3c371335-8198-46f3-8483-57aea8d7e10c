<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="地磅名称" prop="weighbridgeName">
            <el-input v-model="queryParams.weighbridgeName" placeholder="请输入地磅名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="地磅编码" prop="weighbridgeCode">
            <el-input v-model="queryParams.weighbridgeCode" placeholder="请输入地磅编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['system:weighbridge:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['system:weighbridge:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['system:weighbridge:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['system:weighbridge:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="weighbridgeList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="地磅id" align="center" prop="weighbridgeId" v-if="false" />
        <el-table-column label="地磅名称" align="center" prop="weighbridgeName" />
        <el-table-column label="地磅编码" align="center" prop="weighbridgeCode" />
        <el-table-column label="网络路径" align="center" prop="wsUrl" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:weighbridge:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['system:weighbridge:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改地磅对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :close-on-click-modal="false">
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="地磅名称" prop="weighbridgeName">
                <el-input v-model="form.weighbridgeName" placeholder="请输入地磅名称" />
            </el-form-item>
            <el-form-item label="地磅编码" prop="weighbridgeCode">
                <el-input v-model="form.weighbridgeCode" placeholder="请输入地磅编码" />
            </el-form-item>
            <el-form-item label="网络路径" prop="wsUrl">
                <el-input v-model="form.wsUrl" type="textarea" placeholder="请输入内容" />
            </el-form-item>
            <el-form-item label="备用1" prop="spare1" v-if="false">
                <el-input v-model="form.spare1" placeholder="请输入备用1" />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listWeighbridge,
    getWeighbridge,
    delWeighbridge,
    addWeighbridge,
    updateWeighbridge
} from "@/api/system/weighbridge";

export default {
    name: "Weighbridge",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 地磅表格数据
            weighbridgeList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                weighbridgeName: null,
                weighbridgeCode: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询地磅列表 */
        getList() {
            this.loading = true;
            listWeighbridge(this.queryParams).then(response => {
                this.weighbridgeList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                weighbridgeId: null,
                weighbridgeName: null,
                weighbridgeCode: null,
                wsUrl: null,
                spare1: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.weighbridgeId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加地磅";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const weighbridgeId = row.weighbridgeId || this.ids
            getWeighbridge(weighbridgeId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改地磅";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.weighbridgeId != null) {
                        updateWeighbridge(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addWeighbridge(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const weighbridgeIds = row.weighbridgeId || this.ids;
            this.$modal.confirm('是否确认删除地磅编号为"' + weighbridgeIds + '"的数据项？').then(function () {
                return delWeighbridge(weighbridgeIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('system/weighbridge/export', {
                ...this.queryParams
            }, `weighbridge_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
