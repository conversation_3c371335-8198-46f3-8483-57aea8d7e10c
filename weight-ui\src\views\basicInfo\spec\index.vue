<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
        <el-form-item label="规格编号" prop="specCode">
            <el-input v-model="queryParams.specCode" placeholder="请输入规格编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="规格名称" prop="specName">
            <el-input v-model="queryParams.specName" placeholder="请输入规格名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="默认值状态" prop="defaultStatus" v-if="false">
            <el-select v-model="queryParams.defaultStatus" placeholder="请选择默认值状态" clearable>
                <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['basicInfo:spec:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['basicInfo:spec:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['basicInfo:spec:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['basicInfo:spec:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="specList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="物料规格ID" align="center" prop="specId" v-if="false" />
        <el-table-column label="规格编号" align="center" prop="specCode" />
        <el-table-column label="规格名称" align="center" prop="specName" />
        <el-table-column label="物料名称" align="center" prop="materialId" />
        <el-table-column label="部门名称" align="center" prop="deptId" v-if="false"/>
        <el-table-column label="单价" align="center" prop="price" v-if="false"/>
        <el-table-column label="型号" align="center" prop="model" v-if="false"/>
        <el-table-column label="材质" align="center" prop="texture" v-if="false"/>
        <el-table-column label="密度" align="center" prop="thickness" v-if="false"/>
        <el-table-column label="厚度单位标识符" align="center" prop="thickUnit" v-if="false"/>
        <el-table-column label="计量单位" align="center" prop="wtUnit" v-if="false"/>
        <el-table-column label="默认值状态" align="center" prop="defaultStatus" v-if="false">
            <template slot-scope="scope">
                <el-switch v-model="scope.row.defaultStatus" active-value="0" inactive-value="1" @change="handleDefaultStatusChange(scope.row)"></el-switch>
            </template>
        </el-table-column>
        <el-table-column label="启用状态" align="center" prop="status">
            <template slot-scope="scope">
                <el-switch v-model="scope.row.status" active-value="Y" inactive-value="N" @change="handleStatusChange(scope.row)"></el-switch>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['basicInfo:spec:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['basicInfo:spec:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改物料规格对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="110px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="规格编号" prop="specCode" v-if="false">
                        <el-input v-model="form.specCode" placeholder="请输入规格编号" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="规格名称" prop="specName">
                        <el-input v-model="form.specName" placeholder="请输入规格名称" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" v-if="false">
                <el-col :span="12">
                    <el-form-item label="单价" prop="price">
                        <el-input v-model="form.price" placeholder="请输入单价" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="型号" prop="model">
                        <el-input v-model="form.model" placeholder="请输入型号" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" v-if="false">
                <el-col :span="12">
                    <el-form-item label="材质" prop="texture">
                        <el-input v-model="form.texture" placeholder="请输入材质" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="密度" prop="thickness">
                        <el-input v-model="form.thickness" placeholder="请输入密度" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" v-if="false">
                <el-col :span="12">
                    <el-form-item label="厚度单位标识符" prop="thickUnit">
                        <el-input v-model="form.thickUnit" placeholder="请输入厚度单位标识符" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="计量单位" prop="wtUnit">
                        <el-input v-model="form.wtUnit" placeholder="请输入计量单位" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                    <el-form-item label="物料名称" prop="materialId">
                        <el-select v-model="form.materialId" filterable placeholder="请选择物料名称">
                            <el-option v-for="dict in materialList" :key="dict.materialId" :label="dict.materialName" :value="dict.materialId"></el-option>
                        </el-select>
                    </el-form-item>
              </el-col>
                <el-col :span="12">
                    <el-form-item label="启用状态" prop="status">
                        <el-select v-model="form.status" placeholder="请选择状态" style="width:100%">
                            <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="备用1" prop="spare1" v-if="false">
                <el-input v-model="form.spare1" placeholder="请输入备用1" />
            </el-form-item>
            <el-form-item label="备用2" prop="spare2" v-if="false">
                <el-input v-model="form.spare2" placeholder="请输入备用2" />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listSpec,
    getSpec,
    delSpec,
    addSpec,
    updateSpec
} from "@/api/basicInfo/spec";
import {
    listMaterial,
} from "@/api/basicInfo/material";

export default {
    name: "Spec",
    dicts: ['sys_normal_disable','sys_yes_no'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 物料规格表格数据
            specList: [],
            materialList: [],//物料列表
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                specCode: null,
                specName: null,
                deptId: null,
                defaultStatus: null,
                materialId: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                specCode: [{
                    required: true,
                    message: "规格编号不能为空",
                    trigger: "blur"
                }],
                specName: [{
                    required: true,
                    message: "规格名称不能为空",
                    trigger: "blur"
                }],
                materialId: [{
                    required: true,
                    message: "物料名称不能为空",
                    trigger: "change"
                }],
            },
            // 新增：提交按钮loading
            submitLoading: false
        };
    },
    created() {
        this.getList();
        this.init();
    },
    methods: {
        /** 查询物料规格列表 */
        getList() {
            this.loading = true;
            listSpec(this.queryParams).then(response => {
                this.specList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        //数据初始化
        init() {
          // 获取物料列表
          listMaterial().then(response => {
              this.materialList = response.rows;
          })
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                specId: null,
                specCode: null,
                specName: null,
                deptId: null,
                price: null,
                model: null,
                texture: null,
                thickness: null,
                thickUnit: null,
                wtUnit: null,
                defaultStatus: null,
                materialId: null,
                spare1: null,
                spare2: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 规格状态修改
        handleStatusChange(row) {
            let text = row.status === "Y" ? "启用" : "停用";
            this.$modal.confirm('确认要"' + text + '""' + row.specName + '"规格吗？').then(() => {
                return updateSpec(row);
            }).then(() => {
                this.$modal.msgSuccess(text + "成功");
            }).catch(() => {
                row.status = row.status === "Y" ? "N" : "Y";
            });
        },
        // 规格默认值状态修改
        handleDefaultStatusChange(row) {
            let text = row.defaultStatus === "0" ? "设为正常" : "设为停用";
            this.$modal.confirm('确认要"' + text + '""' + row.specName + '"规格的默认值状态吗？').then(() => {
                return updateSpec(row);
            }).then(() => {
                this.$modal.msgSuccess(text + "成功");
            }).catch(() => {
                row.defaultStatus = row.defaultStatus === "0" ? "1" : "0";
            });
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.specId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加物料规格";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const specId = row.specId || this.ids
            getSpec(specId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改物料规格";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.submitLoading = true;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.specId != null) {
                        updateSpec(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    } else {
                        addSpec(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    }
                } else {
                    this.submitLoading = false;
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const specIds = row.specId || this.ids;
            this.$modal.confirm('是否确认删除物料规格编号为"' + specIds + '"的数据项？').then(function () {
                return delSpec(specIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('basicInfo/spec/export', {
                ...this.queryParams
            }, `spec_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
