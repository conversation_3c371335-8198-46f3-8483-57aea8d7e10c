import request from '@/utils/request'

// 查询车队列表
export function listFleet(query) {
  return request({
    url: '/basicInfo/fleet/list',
    method: 'get',
    params: query
  })
}

// 查询车队详细
export function getFleet(fleetId) {
  return request({
    url: '/basicInfo/fleet/' + fleetId,
    method: 'get'
  })
}

// 新增车队
export function addFleet(data) {
  return request({
    url: '/basicInfo/fleet',
    method: 'post',
    data: data
  })
}

// 修改车队
export function updateFleet(data) {
  return request({
    url: '/basicInfo/fleet',
    method: 'put',
    data: data
  })
}

// 删除车队
export function delFleet(fleetId) {
  return request({
    url: '/basicInfo/fleet/' + fleetId,
    method: 'delete'
  })
}


