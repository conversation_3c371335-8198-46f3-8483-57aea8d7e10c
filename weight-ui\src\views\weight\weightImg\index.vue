<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="110px">
        <el-form-item label="过磅图片路径" prop="weightImgPath">
            <el-input v-model="queryParams.weightImgPath" placeholder="请输入过磅图片路径" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="过磅图片文件名" prop="weightImgName">
            <el-input v-model="queryParams.weightImgName" placeholder="请输入过磅图片文件名" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['weight:weightImg:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['weight:weightImg:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['weight:weightImg:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['weight:weightImg:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="weightImgList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="过磅图片ID" align="center" prop="weightImgId" v-if="false"/>
        <el-table-column label="称重记录编码" align="center" prop="weightId" />
        <el-table-column label="过磅图片路径" align="center" prop="weightImgPath" />
        <el-table-column label="过磅图片文件名" align="center" prop="weightImgName" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['weight:weightImg:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['weight:weightImg:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改过磅图片对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="110px">
            <el-form-item label="称重记录编码" prop="weightId">
                <el-select v-model="form.weightId" placeholder="请选择称重记录编码" style="width: 100%;">
                    <el-option v-for="item in weightList" :key="item.weightId" :label="item.weightCode" :value="item.weightId">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="过磅图片路径" prop="weightImgPath">
                <el-input v-model="form.weightImgPath" placeholder="请输入过磅图片路径" />
            </el-form-item>
            <el-form-item label="过磅图片文件名" prop="weightImgName">
                <el-input v-model="form.weightImgName" placeholder="请输入过磅图片文件名" />
            </el-form-item>
            <el-form-item label="备用1" prop="spare1" v-if="false">
                <el-input v-model="form.spare1" placeholder="请输入备用1" />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listWeightImg,
    getWeightImg,
    delWeightImg,
    addWeightImg,
    updateWeightImg
} from "@/api/weight/weightImg";
import {
    listWeight,
} from "@/api/weight/weight";

export default {
    name: "WeightImg",
    data() {
        return {
            // 遮罩层
            loading: true,
            //称重记录集合
            weightList: [],
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 过磅图片表格数据
            weightImgList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                weightId: null,
                weightImgPath: null,
                weightImgName: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询过磅图片列表 */
        getList() {
            this.loading = true;
            listWeightImg(this.queryParams).then(response => {
                this.weightImgList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
            this.getWeighbridgeList()
        },
        //称重列表查询
        async getWeighbridgeList() {
            try {
                const response = await listWeight();
                this.weightList = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                weightImgId: null,
                weightId: null,
                weightImgPath: null,
                weightImgName: null,
                spare1: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.weightImgId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加过磅图片";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            console.log(row);
            
            const weightImgId = row.weightImgId || this.ids
            getWeightImg(weightImgId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改过磅图片";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.weightImgId != null) {
                        updateWeightImg(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addWeightImg(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const weightImgIds = row.weightImgId || this.ids;
            this.$modal.confirm('是否确认删除过磅图片编号为"' + weightImgIds + '"的数据项？').then(function () {
                return delWeightImg(weightImgIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('weight/weightImg/export', {
                ...this.queryParams
            }, `weightImg_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
