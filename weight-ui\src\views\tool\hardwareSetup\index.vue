<template>
    <div class="hello">
        <div class="layout-container">
            <!-- 侧边导航栏 - 添加折叠功能 -->
            <el-menu 
                class="side-menu"
                :default-active="activeType"
                @select="handleMenuSelect"
                :collapse="isCollapse"
                background-color="#545c64"
                text-color="#fff"
                active-text-color="#ffd04b"
            >
                <div class="menu-toggle" @click="isCollapse = !isCollapse">
                    <i :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
                    <span v-if="!isCollapse" style="margin-left: 10px">华星硬件服务配置系统</span>
                </div>
                <el-menu-item 
                    v-for="item in menuList" 
                    :key="item.type" 
                    :index="item.type"
                >
                    <i class="el-icon-menu"></i>
                    <span slot="title">{{ item.type }}</span>
                </el-menu-item>
            </el-menu>
    
            <!-- 主内容区 -->
            <div class="main-content">
                <el-card shadow="hover" class="content-card">
                    <div class="table-header">
                        <h2>系统配置列表</h2>
                        <img src="../../../assets/logo.gif" alt="">
                    </div>
                    <el-table 
                        :data="appointmentList" 
                        border 
                        stripe 
                        style="width: 100%"
                        row-key="id"
                        v-loading="loading"
                        :header-cell-style="{background: '#f5f7fa', color: '#606266'}"
                    >
                        <el-table-column label="序号" align="center" prop="id" width="80" />
                        <el-table-column label="配置节点key" align="center" prop="code" />
                        <el-table-column label="配置节点称" align="center" prop="name" />
                        <el-table-column label="配置节点描述" align="center" prop="description" show-overflow-tooltip />
                        <el-table-column label="配置节点值" align="center" prop="value" />
                        <el-table-column label="操作" align="center" width="120">
                            <template slot-scope="scope">
                                <el-button size="mini" type="primary" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-card>
            </div>
        </div>
    
        <el-dialog title="修改配置节点" :visible.sync="dialogVisible" width="600px" :close-on-click-modal="false">
            <el-form ref="form" :model="form" :rules="rules" label-width="100px" label-position="left">
                <el-form-item label="配置编码" prop="code">
                    <el-input v-model="form.code" placeholder="请输入配置编码" clearable :disabled="true"/>
                </el-form-item>
                <el-form-item label="配置名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入配置名称" clearable :disabled="true"/>
                </el-form-item>
                <el-form-item label="配置描述" prop="description">
                    <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入配置描述" />
                </el-form-item>
                <el-form-item label="配置值" prop="value">
                    <el-input v-model="form.value" placeholder="请输入配置值" clearable />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-dialog>
    
        <!-- 著作标识 -->
        <div class="copyright">
            © 2025 华星系统配置管理系统 - 版权所有
        </div>
    </div>
    </template>
    
    <script>
    export default {
        name: 'hardwareSetup',
        data() {
            return {
                loading: true,
                appointmentList: [],
                menuList: [],
                form: {},
                rules: {
                    code: [{ required: true, message: '配置编码不能为空', trigger: 'blur' }],
                    name: [{ required: true, message: '配置名称不能为空', trigger: 'blur' }],
                    value: [{ required: true, message: '配置值不能为空', trigger: 'blur' }]
                },
                dialogVisible: false,
                activeType: '',
                isCollapse: false,
                httpUrl: 'http://*************:18080',
            };
        },
        async mounted() {
            this.httpUrl = await this.readFile('./config.txt')
            console.log(this.httpUrl, 'httpUrl');
            this.getMenuList();
        },
        methods: {
            // 获取侧边栏菜单
            getMenuList() {
                this.$axios.post(this.httpUrl +'/sysConfig/queryConfigList', {})
                    .then(res => {
                        this.menuList = res.data.data;
                        if(this.menuList.length > 0) {
                            this.activeType = this.menuList[0].type;
                            this.getList(this.activeType);
                        }
                    });
            },
            // 菜单选择事件
            handleMenuSelect(type) {
                this.activeType = type;
                this.getList(type);
            },
            // 获取表格数据
            getList(type) {
                this.loading = true;
                this.$axios.post(this.httpUrl +'/sysConfig/queryConfigList', {type})
                    .then(res => {
                        this.appointmentList = res.data.data[0]?.sysConfigList || [];
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            },
            handleUpdate(row) {
                this.form = JSON.parse(JSON.stringify(row));
                this.dialogVisible = true;
            },
            submitForm() {
                this.$refs['form'].validate(valid => {
                    if (valid) {
                        this.$axios.post(this.httpUrl +'/sysConfig/updateSysConfigList', [this.form])
                            .then(res => {
                                if (res.data.code == 200) {
                                    this.$message.success('修改成功');
                                    this.dialogVisible = false;
                                    this.getList(this.activeType);
                                } else {
                                    this.$message.error(res.data.msg);
                                }
                            })
                            .catch(() => {
                                this.$message.error('修改失败');
                            });
                    }
                });
            },
            //读取文件信息
            /**
             * 异步读取文件内容
             * @param {string} filePath - 要读取的文件路径
             * @returns {Promise<string>} 返回一个Promise对象，成功时解析文件内容，失败时返回错误
             */
            readFile(filePath) {
                return new Promise((resolve, reject) => {
                    // 创建XMLHttpRequest对象
                    const xhr = new XMLHttpRequest();
                    // 根据当前协议确定成功状态码(本地文件为0，http请求为200)
                    const okStatus = document.location.protocol === 'file' ? 0 : 200;
                    
                    // 初始化GET请求
                    xhr.open('GET', filePath, true);
                    // 设置响应类型为UTF-8编码的文本
                    xhr.overrideMimeType('text/html;charset=utf-8');
                    
                    // 请求成功回调
                    xhr.onload = () => {
                        if (xhr.status === okStatus) {
                            // 请求成功，返回文件内容
                            resolve(xhr.responseText);
                        }else {
                            // 请求失败，返回错误
                            reject(new Error('Failed to load file'));
                        }
                    };
                    
                    // 网络错误回调
                    xhr.onerror = () => {
                        reject(new Error('Network error'));
                    };
                    
                    // 发送请求
                    xhr.send(null);
                });
            },
        },
    }
    </script>
    
    <style scoped lang="scss">
    .hello {
        height: 100%;
        background: #f0f2f5;
        ::v-deep .el-menu {
            background-color: #545c64 !important;
        }
    }
    .layout-container {
        display: flex;
        height: calc(100vh - 60px);
    }
    .side-menu {
        min-height: 100%;
        transition: width 0.3s;
        border-right: none;
    }
    .side-menu:not(.el-menu--collapse) {
        width: 300px;
        overflow-y: auto;
        min-height: calc(100vh - 100px);
    }
    .menu-toggle {
        padding: 20px;
        text-align: left;
        color: #fff;
        cursor: pointer;
        border-bottom: 1px solid #4a4f57;
    }
    .menu-toggle:hover {
        background-color: #434a50;
    }
    .main-content {
        flex: 1;
        padding: 20px;
        overflow: auto;
    }
    .content-card {
        min-height: calc(100vh - 102px);
    }
    .el-menu-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
    .el-menu-item [class^=el-icon-] {
        margin-right: 5px;
        width: 24px;
        text-align: left;
        font-size: 18px;
        vertical-align: middle;
    }
    .table-header {
        padding: 15px 0;
        border-bottom: 1px solid #ebeef5;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .table-header h2 {
        margin: 0;
        color: #303133;
    }
    .copyright {
        text-align: center;
        width: 100%;
    }
    </style>    