<template>
<div class="box_center" style="height: 100%">
    <div class="head">
        <h1><a>智能称重系统驾驶舱</a></h1>
        <div class="time" id="showTime">{{ time }}</div>
    </div>
    <div class="mainbox">
        <ul class="clearfix">
            <li>
                <div class="boxall" style="height: calc(58% - 0.15rem)">
                    <div class="alltitle">物料总发货数</div>
                    <div class="boxnav echarts4" id="echarts4"></div>
                </div>
                <div class="boxall" style="height: calc(42% - 0.15rem)">
                    <div class="alltitle">近七天物料出货统计</div>
                    <div class="boxnav echarts3" id="echarts3"></div>
                </div>
            </li>
            <li>
                <div class="boxall" style="height: calc(20% - 0.15rem)">
                    <ul class="row h100 clearfix">
                        <li class="col-6">
                            <div class="sqzs h100">
                                <p>今日出货量</p>
                                <h1>
                                    <span>{{ todaySum }}</span>吨
                                </h1>
                            </div>
                        </li>
                        <li class="col-6">
                            <div class="sqzs h100">
                                <p>总出货量</p>
                                <h1>
                                    <span>{{ totalSum }}</span>吨
                                </h1>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="boxall" style="height: calc(38% - 0.15rem)">
                    <div class="alltitle">年物料出货数</div>
                    <div class="boxnav echarts1" id="echarts1"></div>
                </div>
                <div class="boxall" style="height: calc(42% - 0.15rem)">
                    <div class="alltitle">月物料出货数</div>
                    <div class="boxnav echarts2" id="echarts2"></div>
                </div>
            </li>
            <li>
                <div class="boxall" style="height: calc(33.333% - 0.15rem)">
                    <div class="alltitle">客户物料发货数</div>
                    <div class="boxnav echarts5" id="echarts5"></div>
                </div>
                <div class="boxall" style="height: calc(33.333% - 0.15rem);overflow: auto;">
                    <div class="alltitle">物料出货数统计</div>
                    <div class="boxnav">
                        <table border="0" cellspacing="0">
                            <tbody>
                                <tr>
                                    <th>物料名称</th>
                                    <th>日发货数</th>
                                    <th>周发货数</th>
                                    <th>月发货数</th>
                                    <th>年发货数</th>
                                </tr>
                                <tr v-for="item in reportList" :key="item.id || item.specName">
                                    <th>{{ item.specName }}</th>
                                    <td>{{ item.daySum }}</td>
                                    <td>{{ item.aweekSum }}</td>
                                    <td>{{ item.monSum }}</td>
                                    <td>{{ item.yearSum }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="boxall" style="height: calc(33.333% - 0.15rem)">
                    <div class="alltitle">今日物料出货数</div>
                    <div class="boxnav echarts6" id="echarts6" style="height: calc(100% - 0.3rem)"></div>
                </div>
            </li>
        </ul>
    </div>
</div>
</template>

<script>
// import {
//     listReport,
//     todayList,
//     aWeekList,
//     aWeekTotalList,
//     todaySum,
//     totalSum,
//     trainNumber,
//     customerList,
//     aWeekYearList,
//     specForms
// } from "@/api/report/graphical";

// 静态常量抽离
const COLOR_ARR = [
    { colorsOne: '#8bd46e', colorsTwo: '#09bcb7' },
    { colorsOne: '#248ff7', colorsTwo: '#6851f1' },
    { colorsOne: '#fccb05', colorsTwo: '#f5804d' },
    { colorsOne: '#7CF7FF', colorsTwo: '#4B73FF' },
    { colorsOne: '#8A88FB', colorsTwo: '#D079EE' },
    { colorsOne: '#FFED46', colorsTwo: '#FF7EC7' },
    { colorsOne: '#8FFF85', colorsTwo: '#39A0FF' },
    { colorsOne: '#E0FF87', colorsTwo: '#8FB85B' },
    { colorsOne: '#FFDC99', colorsTwo: '#FF62C0' },
    { colorsOne: '#DDE4FF', colorsTwo: '#8DA2EE' },
];

export default {
    name: "GraphicReport",
    data() {
        return {
            //遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 总条数
            total: 0,
            time: null,
            timer: null,
            //报表详情表格数据
            xAxisData: [],
            seriesList: [],
            // 报表详情表格数据
            reportList: [],
            todaySum: 0,
            totalSum: 0,
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                queryField: "0",
                queryCondition: "0",
                beginTime: "2021-1-1",
                endTime: "2100-12-31",
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
            chartTitle: "柱状图报表",
            chartDataName: "目录",
            pickerTime: [],
            colorArr: COLOR_ARR,
            queryFieldOptions: [],
            queryConditionOptions: [],
            option: {},
            optionBar: {},
            optionCenter: {},
            optionTopRight: {},
            optionLowerLeft: {},
            optionBottom: {},
            optionCategory: {},
        };
    },
    created() {
        // `this` 指向 vm 实例
        // this.getList();
    },
    watch: {
        "queryParams.queryField": function (val) {
            if (val) {
                this.fetchReportList();
            }
        },
        "queryParams.queryCondition": function (val) {
            if (val) {
                this.fetchReportList();
                if (this.queryParams.queryCondition === "3" && this.queryParams.queryField !== "3") {
                    this.msgWarning("请选择了物料再选择规格");
                    this.queryParams.queryCondition = "0";
                }
            }
        },
    },
    mounted() {
        this.setHtmlFontSize();
        window.addEventListener("resize", this.setHtmlFontSize);
        this.toggleFullScreen()
        // this.setPx();
        //页面加载完成后,才执行
        // this.timer = setInterval(() => {
        //     setTimeout(this.getNum(), 0)
        // }, 1000 * 7200)
        this.getTime();
        // this.getNum();
    },
    beforeDestroy() {
        clearInterval(this.timer);
        this.timer = null;
        window.removeEventListener("resize", this.setHtmlFontSize);
    },
    methods: {
        //屏幕自适应
        setHtmlFontSize() {
            const whei = window.innerWidth;
            document.querySelector("html").style.fontSize = whei / 20 + "px";
        },
        //实时时间
        getTime() {
            clearTimeout(this.timer);
            const dt = new Date();
            const y = dt.getFullYear();
            const mt = dt.getMonth() + 1;
            const day = dt.getDate();
            const h = dt.getHours(); //获取时
            const m = dt.getMinutes(); //获取分
            const s = dt.getSeconds(); //获取秒
            this.time =
                y +
                "/" +
                this.appendZero(mt) +
                "/" +
                this.appendZero(day) +
                " " +
                this.appendZero(h) +
                ":" +
                this.appendZero(m) +
                ":" +
                this.appendZero(s) +
                "";
            this.timer = setTimeout(this.getTime, 1000); //设定定时器，循环运行
        },
        appendZero(num) {
            return num < 10 ? "0" + num : num;
        },
        getNum() {
            //年物料发货数
            specForms({
                spare1: 1
            }).then(res => {
                this.reportList = res.data
            })
            //年物料发货数
            aWeekYearList({
                spare1: 1,
            }).then((res) => {
                this.optionCenter.legend.data = []
                this.optionCenter.series = []
                res.data.map((item, index) => {
                    this.optionCenter.legend.data.push(item.specName)
                    this.optionCenter.xAxis[0].data = item.sumdata.days
                    this.optionCenter.series.push({
                        name: item.specName,
                        type: "bar",
                        data: item.sumdata.materialNames,
                        barWidth: "15%",
                        itemStyle: {
                            normal: {
                                barBorderRadius: 15,
                                color: this.colorArr[index].colorsOne,
                            },
                        },
                        barGap: "0.2",
                    }, {
                        name: item.specName,
                        type: "line",
                        yAxisIndex: 1,
                        data: item.sumdata.incrementalRs,
                        lineStyle: {
                            normal: {
                                width: 2,
                            },
                        },
                        itemStyle: {
                            normal: {
                                color: this.colorArr[index].colorsOne,
                            },
                        },
                        smooth: true,
                    })
                })
                this.showChart2();
            });
            //今日物料发货数
            todayList({
                spare1: 1,
            }).then((res) => {
                let num = 0;
                this.optionBar.series[0].color = []
                res.data.map((item, index) => {
                    num += item.sunNetWeight;
                    item.value = item.sunNetWeight;
                    item.name = item.materialName;
                    this.optionBar.series[0].color.push(this.colorArr[index].colorsTwo)
                });
                this.optionBar.title.text = (num).toFixed(2);
                this.optionBar.series[0].data = res.data;
                this.xAxisData = res.data;
                this.showChart1();
            });
            //月物料发货数
            aWeekList({
                spare1: 1,
            }).then((res) => {
                this.optionBottom.legend.data = []
                this.optionBottom.series = []
                res.data.map((item, index) => {
                    this.optionBottom.legend.data.push(item.specName)
                    this.optionBottom.xAxis[0].data = item.sumdata.days
                    this.optionBottom.series.push({
                        name: item.specName,
                        type: "line",
                        smooth: true,
                        symbol: "circle",
                        symbolSize: 5,
                        showSymbol: false,
                        lineStyle: {
                            normal: {
                                color: this.colorArr[index].colorsOne,
                                width: 2,
                            },
                        },
                        areaStyle: {
                            normal: {
                                color: new this.$echarts.graphic.LinearGradient(
                                    0,
                                    0,
                                    0,
                                    1,
                                    [{
                                            offset: 0,
                                            color: this.colorArr[index].colorsOne,
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(228, 228, 126, 0)",
                                        },
                                    ],
                                    false
                                ),
                                shadowColor: "rgba(0, 0, 0, 0.1)",
                            },
                        },
                        itemStyle: {
                            normal: {
                                color: this.colorArr[index].colorsOne,
                                borderColor: "rgba(228, 228, 126, .1)",
                                borderWidth: 12,
                            },
                        },
                        data: item.sumdata.materialNames,
                    })
                })
                this.showChart6();
            });
            //周总发货量
            aWeekTotalList({
                spare1: 1,
            }).then((res) => {
                this.option.yAxis[0].data = [];
                this.option.yAxis[1].data = [];
                this.option.series[0].data = [];
                let arr = res.data.specDatas.sort(function (a, b) {
                    return a.percentage - b.percentage
                })
                arr.forEach(element => {
                    this.option.yAxis[0].data.push(element.name)
                    this.option.yAxis[1].data.push(element.netweight)
                    this.option.series[0].data.push(element.percentage)
                });
                this.showChart4();
            });
            //今日物料出货量
            todaySum({
                spare1: 1,
            }).then((res) => {
                this.todaySum = res.data;
            });
            //总物料出货量
            totalSum({
                spare1: 1,
            }).then((res) => {
                this.totalSum = res.data;
            });
            //近七天出货量统计
            trainNumber({
                spare1: 1,
            }).then((res) => {
                this.optionLowerLeft.legend.data = [];
                this.optionLowerLeft.series = [];
                res.data.map((item, index) => {
                    this.optionLowerLeft.legend.data.push(item.specName);
                    this.optionLowerLeft.xAxis.data = item.sumdata.days;
                    this.optionLowerLeft.series.push({
                        name: item.specName,
                        type: "bar",
                        stack: "a",
                        barWidth: "30",
                        barGap: 0,
                        itemStyle: {
                            normal: {
                                color: this.colorArr[Math.floor(index)].colorsOne,
                            },
                        },
                        data: item.sumdata.materialNames,
                    });
                });
                // this.optionLowerLeft
                this.showChart5();
            });
            //客户物料发货数
            customerList({
                spare1: 1,
            }).then((res) => {
                console.log(res);
                this.optionTopRight.xAxis[0].data = res.data.sumdata.customerNames
                this.optionTopRight.series[0].data = res.data.sumdata.sunNetWeights
                // this.optionTopRight.series[1].data = res.data.sumdata.sunNetWeights
                this.showChart3();
            });
        },
        randomHexColor() {
            //随机生成十六进制颜色
            var hex = Math.floor(Math.random() * 1546655684122).toString(16); //生成ffffff以内16进制数
            while (hex.length < 6) {
                //while循环判断hex位数，少于6位前面加0凑够6位
                hex = "0" + hex;
            }
            return "#" + hex; //返回'#'开头16进制颜色
        },
        //自动全屏
        toggleFullScreen() {
            const element = document.documentElement;
            if (!document.fullscreenElement) {
                element.requestFullscreen().catch((err) => {
                    console.log(
                        `Error attempting to enable full-screen mode: ${err.message} (${err.name})`
                    );
                });
            } else {
                document.exitFullscreen();
            }
        },
        /** 报表详情查询 */
        async fetchReportList() {
            this.loading = true;
            try {
                const [fieldRes, condRes, reportRes] = await Promise.all([
                    this.getDicts("query_field"),
                    this.getDicts("query_condition"),
                    this.listReport(this.queryParams),
                ]);
                this.queryFieldOptions = fieldRes.data;
                this.queryConditionOptions = condRes.data;
                this.xAxisData = reportRes.xAxisData;
                this.seriesList = reportRes.seriesList;
            } catch (e) {
                // 错误处理
                this.$message.error("获取报表数据失败");
            } finally {
                this.loading = false;
            }
        },
        // 表单重置
        resetFormFields() {
            this.form = {
                queryField: undefined,
                queryCondition: undefined,
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            //时间选择
            if (this.pickerTime[0] != null && this.pickerTime[1] != null) {
                this.queryParams.beginTime = this.pickerTime[0];
                this.queryParams.endTime = this.pickerTime[1];
            }
            this.fetchReportList();
        },
        setPx() {
            var docEl = document.documentElement;
            var dpr = window.devicePixelRatio || 1;

            // adjust body font size
            function setBodyFontSize() {
                if (document.body) {
                    document.body.style.fontSize = 12 * dpr + "px";
                } else {
                    document.addEventListener("DOMContentLoaded", setBodyFontSize);
                }
            }
            setBodyFontSize();

            // set 1rem = viewWidth / 10
            function setRemUnit() {
                var rem = docEl.clientWidth / 24;
                docEl.style.fontSize = rem + "px";
            }

            setRemUnit();

            // reset rem unit on page resize
            window.addEventListener("resize", setRemUnit);
            window.addEventListener("pageshow", function (e) {
                if (e.persisted) {
                    setRemUnit();
                }
            });

            // detect 0.5px supports
            if (dpr >= 2) {
                var fakeBody = document.createElement("body");
                var testElement = document.createElement("div");
                testElement.style.border = ".5px solid transparent";
                fakeBody.appendChild(testElement);
                docEl.appendChild(fakeBody);
                if (testElement.offsetHeight === 1) {
                    docEl.classList.add("hairlines");
                }
                docEl.removeChild(fakeBody);
            }
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
            this.pickerTime = [];
        },
        //饼图
        showChart1() {
            var myChart = this.$echarts.init(document.querySelector(".echarts6"));
            myChart.setOption(this.optionBar);
            window.addEventListener("resize", function () {
                myChart.resize();
            });
        },
        //中间折柱线图
        showChart2() {
            var myChart = this.$echarts.init(document.querySelector(".echarts1"));
            myChart.setOption(this.optionCenter);
            window.addEventListener("resize", function () {
                myChart.resize();
            });
        },
        showChart3() {
            var myChart = this.$echarts.init(document.querySelector(".echarts5"));
            myChart.setOption(this.optionTopRight);
            window.addEventListener("resize", function () {
                myChart.resize();
            });
        },
        showChart4() {
            var myChart = this.$echarts.init(document.querySelector(".echarts4"));
            myChart.setOption(this.option);
            window.addEventListener("resize", function () {
                myChart.resize();
            });
        },
        showChart5() {
            var myChart = this.$echarts.init(document.querySelector(".echarts3"));
            myChart.setOption(this.optionLowerLeft);
            window.addEventListener("resize", function () {
                myChart.resize();
            });
        },
        showChart6() {
            var myChart = this.$echarts.init(document.querySelector(".echarts2"));
            myChart.setOption(this.optionBottom);
            window.addEventListener("resize", function () {
                myChart.resize();
            });
        },
    },
};
</script>

<style scoped>
@charset "utf-8";

/* CSS Document */
* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

*,
.box_center {
    padding: 0px;
    margin: 0px;
    /* height: 100%; */
    /* height: 1080px; */
    font-family: "微软雅黑";
}

.box_center {
    background: #000d4a url(../../../assets/images/bg.jpg) center center;
    background-size: cover;
    color: #fff;
    font-size: 0.1rem;
}

li {
    list-style-type: none;
}

@font-face {
    font-family: electronicFont;
    src: url(../../../assets/font/DS-DIGIT.TTF);
}

i {
    margin: 0px;
    padding: 0px;
    text-indent: 0px;
}

img {
    border: none;
    max-width: 100%;
}

a {
    text-decoration: none;
    color: #399bff;
}

a.active,
a:focus {
    outline: none !important;
    text-decoration: none;
}

ol,
ul,
p,
h1,
h2,
h3,
h4,
h5,
h6 {
    padding: 0;
    margin: 0;
}

a:hover {
    color: #06c;
    text-decoration: none !important;
}

html,
body {
    height: 100%;
}

.clearfix:after,
.clearfix:before {
    display: table;
    content: " ";
}

.clearfix:after {
    clear: both;
}

.pulll_left {
    float: left;
}

.pulll_right {
    float: right;
}

/*谷哥滚动条样式*/
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    position: absolute;
}

::-webkit-scrollbar-thumb {
    background-color: #5bc0de;
}

::-webkit-scrollbar-track {
    background-color: #ddd;
}

.head {
    height: 1.05rem;
    background: url(../../../assets/images/head_bg.png) no-repeat center center;
    background-size: 100% 100%;
    position: relative;
}

.head h1 {
    color: #fff;
    text-align: center;
    font-size: 0.4rem;
    line-height: 0.8rem;
    letter-spacing: -1px;
}

.head h1 img {
    width: 1.5rem;
    display: inline-block;
    vertical-align: middle;
}

.time {
    position: absolute;
    right: 0.15rem;
    top: 0;
    line-height: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.3rem;
    padding-right: 0.1rem;
    font-family: electronicFont;
}

.mainbox {
    padding: 0 0.2rem 0rem 0.2rem;
    height: calc(100% - 1.05rem);
}

.mainbox>ul {
    margin-left: -0.1rem;
    margin-right: -0.1rem;
    height: 100%;
}

.mainbox>ul>li {
    float: left;
    padding: 0 0.1rem;
    height: 100%;
    width: 30%;
}

.mainbox>ul>li:nth-child(2) {
    width: 40%;
}

.boxall {
    padding: 0 0.2rem 0.2rem 0.2rem;
    background: rgba(6, 48, 109, 0.5);
    position: relative;
    margin-bottom: 0.15rem;
    z-index: 10;
}

.alltitle {
    font-size: 0.2rem;
    color: #fff;
    line-height: 0.5rem;
    position: relative;
    padding-left: 0.15rem;
}

.alltitle:before {
    position: absolute;
    height: 0.2rem;
    width: 4px;
    background: #49bcf7;
    border-radius: 5px;
    content: "";
    left: 0;
    top: 50%;
    margin-top: -0.1rem;
}

.boxnav {
    height: calc(100% - 0.5rem);
}

.row>li {
    float: left;
    height: 100%;
}

.col-6 {
    width: 50%;
}

.col-3 {
    width: 25%;
}

.col-4 {
    width: 33.33333%;
}

.h100 {
    height: 100%;
}

.tit01 {
    text-align: center;
    color: white;
    font-size: 0.16rem;
    height: fit-content;
    padding: 0.3rem 0 0.02rem 0;
}

.piebox {
    height: calc(100% - 0.5rem);
    position: relative;
}

.piebox:before {
    width: 0.6rem;
    height: 0.6rem;
    content: "";
    border: 1px solid #49bcf7;
    border-radius: 1rem;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -0.31rem;
    margin-top: -0.31rem;
    opacity: 0.7;
}

.sqzs {
    margin-right: 0.2rem;
}

.sqzs p {
    padding: 0.2rem 0 0.1rem 0;
    font-size: 0.22rem;
    display: block;
    /* height: fit-content; */
}

.sqzs h1 {
    height: calc(100% - 0.65rem);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    color: #fef000;
    font-weight: normal;
    letter-spacing: 2px;
    font-size: 0.5rem;
    justify-content: center;
    padding-bottom: 0.05rem;
}

.sqzs h1 span {
    font-size: 0.6rem;
    font-family: Impact, Haettenschweiler, "Arial Narrow Bold", sans-serif;
}

table {
    width: 100%;
    text-align: center;
}

table th {
    font-size: 0.16rem;
    background: rgba(0, 0, 0, 0.1);
}

table td {
    font-size: 0.16rem;
    color: rgba(255, 255, 255, 0.6);
}

table th,
table td {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0.1rem 0;
}
</style>
