<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
        <el-form-item label="地磅设备名称" prop="wbDeviceName">
            <el-input v-model="queryParams.wbDeviceName" placeholder="请输入地磅设备名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="地磅设备编码" prop="wbDeviceCode">
            <el-input v-model="queryParams.wbDeviceCode" placeholder="请输入地磅设备编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['weight:weightDevice:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['weight:weightDevice:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['weight:weightDevice:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['weight:weightDevice:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="weightDeviceList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="地磅编码" align="center" prop="weighbridgeCode" />
        <el-table-column label="地磅设备名称" align="center" prop="wbDeviceName" />
        <el-table-column label="地磅设备编码" align="center" prop="wbDeviceCode" />
        <el-table-column label="地磅设备类型" align="center" prop="wbDeviceType" />
        <el-table-column label="地磅设备地址" align="center" prop="wbDeviceUrl" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['weight:weightDevice:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['weight:weightDevice:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改地磅设备关联对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-form-item label="地磅设备ID" prop="wbDeviceId" v-if="false">
                <el-input v-model="form.wbDeviceId" placeholder="请输入地磅设备ID" />
            </el-form-item>
            <el-form-item label="地磅编码" prop="weighbridgeCode">
                <el-select v-model="form.weighbridgeCode" placeholder="请选择地磅编码">
                    <el-option v-for="dict in weighbridgeCodeList" :key="dict.weighbridgeCode" :label="dict.weighbridgeCode" :value="dict.weighbridgeCode"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="地磅设备名称" prop="wbDeviceName">
                <el-input v-model="form.wbDeviceName" placeholder="请输入地磅设备名称" />
            </el-form-item>
            <el-form-item label="地磅设备编码" prop="wbDeviceCode">
                <el-input v-model="form.wbDeviceCode" placeholder="请输入地磅设备编码" />
            </el-form-item>
            <el-form-item label="地磅设备地址" prop="wbDeviceUrl">
                <el-input v-model="form.wbDeviceUrl" placeholder="请输入地磅设备地址" />
            </el-form-item>
            <el-form-item label="备用1" prop="spare1" v-if="false">
                <el-input v-model="form.spare1" placeholder="请输入备用1" />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listWeightDevice,
    getWeightDevice,
    delWeightDevice,
    addWeightDevice,
    updateWeightDevice
} from "@/api/weight/weightDevice";
import {
    listWeighbridge,
} from "@/api/system/weighbridge";

export default {
    name: "WeightDevice",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 地磅设备关联表格数据
            weightDeviceList: [],
            // 弹出层标题
            title: "",
            //地磅编号合集
            weighbridgeCodeList: [],
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                weighbridgeCode: null,
                wbDeviceName: null,
                wbDeviceCode: null,
                wbDeviceType: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
        this.init();
    },
    methods: {
        /** 查询地磅设备关联列表 */
        getList() {
            this.loading = true;
            listWeightDevice(this.queryParams).then(response => {
                this.weightDeviceList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        //数据初始化
        init() {
            listWeighbridge().then(response => {
                this.weighbridgeCodeList = response.rows;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                wbDeviceId: null,
                weighbridgeCode: null,
                wbDeviceName: null,
                wbDeviceCode: null,
                wbDeviceType: null,
                wbDeviceUrl: null,
                spare1: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.weighbridgeCode)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加地磅设备关联";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const wbDeviceId = row.wbDeviceId || this.ids
            getWeightDevice(wbDeviceId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改地磅设备关联";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.wbDeviceId != null) {
                        updateWeightDevice(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addWeightDevice(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const weighbridgeCodes = row.weighbridgeCode || this.ids;
            this.$modal.confirm('是否确认删除地磅设备关联编号为"' + weighbridgeCodes + '"的数据项？').then(function () {
                return delWeightDevice(weighbridgeCodes);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('weight/weightDevice/export', {
                ...this.queryParams
            }, `weightDevice_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
