<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="物料编码" prop="materialCode">
            <el-input v-model="queryParams.materialCode" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
            <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['basicInfo:material:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['basicInfo:material:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['basicInfo:material:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['basicInfo:material:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="materialList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="物料ID" align="center" prop="materialId" v-if="false"/>
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column label="物料类别" align="center" prop="categoryId" />
        <el-table-column label="计量类型" align="center" prop="measureType"v-if="false" />
        <el-table-column label="型号规格" align="center" prop="spec" v-if="false"/>
        <el-table-column label="部门名称" align="center" prop="deptId" v-if="false"/>
        <el-table-column label="是否扣杂" align="center" prop="cutFlag" v-if="false"/>
        <el-table-column label="折扣系数" align="center" prop="cutPer" v-if="false"/>
        <el-table-column label="启用状态" align="center" prop="status">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.status" />
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['basicInfo:material:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['basicInfo:material:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改物料对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="物料编码" prop="materialCode">
                        <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="物料名称" prop="materialName">
                        <el-input v-model="form.materialName" placeholder="请输入物料名称" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20" v-if="false">
                <el-col :span="12">
                    <el-form-item label="型号规格" prop="spec">
                        <el-input v-model="form.spec" placeholder="请输入型号规格" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="折扣系数" prop="cutPer">
                        <el-input v-model="form.cutPer" placeholder="请输入折扣系数" />
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="物料类别" prop="categoryId">
                        <el-select v-model="form.categoryId" filterable placeholder="请选择物料类别">
                            <el-option v-for="dict in categoryList" :key="dict.categoryId" :label="dict.categoryName" :value="dict.categoryId"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="启用状态" prop="status">
                        <el-select v-model="form.status" placeholder="请选择状态">
                            <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
            <el-form-item label="备用1" prop="spare1" v-if="false">
                <el-input v-model="form.spare1" placeholder="请输入备用1" />
            </el-form-item>
            <el-form-item label="备用2" prop="spare2" v-if="false">
                <el-input v-model="form.spare2" placeholder="请输入备用2" />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listMaterial,
    getMaterial,
    delMaterial,
    addMaterial,
    updateMaterial
} from "@/api/basicInfo/material";
import {
    listCategory,
} from "@/api/basicInfo/category";

export default {
    name: "Material",
    dicts: ['sys_yes_no','sys_yes_no'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 物料表格数据
            materialList: [],
            categoryList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                materialCode: null,
                materialName: null,
                deptId: null,
                categoryId: null,
                status: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                materialCode: [{
                    required: true,
                    message: "物料编码不能为空",
                    trigger: "blur"
                }],
                materialName: [{
                    required: true,
                    message: "物料名称不能为空",
                    trigger: "blur"
                }],
            },
            // 新增：提交按钮加载动画
            submitLoading: false
        };
    },
    created() {
        this.getList();
        this.init();
    },
    methods: {
        /** 查询物料列表 */
        getList() {
            this.loading = true;
            listMaterial(this.queryParams).then(response => {
                this.materialList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        //数据初始化
        init() {
            listCategory().then(response => {
                this.categoryList = response.rows;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                materialId: null,
                materialCode: null,
                materialName: null,
                measureType: null,
                spec: null,
                deptId: null,
                cutFlag: null,
                cutPer: null,
                categoryId: null,
                spare1: null,
                spare2: null,
                status: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.materialId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加物料";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const materialId = row.materialId || this.ids
            getMaterial(materialId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改物料";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.submitLoading = true;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.materialId != null) {
                        updateMaterial(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    } else {
                        addMaterial(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    }
                } else {
                    this.submitLoading = false;
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const materialIds = row.materialId || this.ids;
            this.$modal.confirm('是否确认删除物料编号为"' + materialIds + '"的数据项？').then(function () {
                return delMaterial(materialIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('basicInfo/material/export', {
                ...this.queryParams
            }, `material_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
