<template>
<div class="drag-drop-container">
    <!-- 拖放区域 -->
    <div class="drop-zone" @dragover="handleDragOver" @dragenter="handleDragEnter" @dragleave="handleDragLeave" @drop="handleDrop">
        <p v-if="!isDragging" class="tip">将图片拖到此处<br>（支持 jpg/png/gif 等格式）</p>
        <p v-if="isDragging" class="tip active">释放鼠标以添加图片</p>
    </div>

    <!-- 图片预览 -->
    <div v-if="previewUrl" class="preview-area">
        <img :src="previewUrl" alt="拖放的图片" class="preview-img">
        <button @click="clearPreview" class="clear-btn">清除预览</button>
    </div>

    <!-- 错误提示 -->
    <p v-if="error" class="error-tip">{{ error }}</p>
</div>
</template>

<script>
export default {
    data() {
        return {
            previewUrl: null, // 图片预览 URL（Blob 或 Data URL）
            isDragging: false, // 拖放状态（用于样式高亮）
            error: '' // 错误信息
        };
    },
    methods: {
        // 拖入时触发（更新状态）
        handleDragEnter(e) {
            e.preventDefault(); // 阻止默认行为（避免浏览器直接打开文件）
            this.isDragging = true;
        },
        // 拖离时触发（更新状态）
        handleDragLeave(e) {
            e.preventDefault();
            // 仅当鼠标完全离开区域时重置状态（避免子元素触发误判）
            if (e.relatedTarget ?.classList ?.contains('drop-zone')) return;
            this.isDragging = false;
        },
        // 拖过时触发（必须阻止默认行为才能触发 drop）
        handleDragOver(e) {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy'; // 设置拖放效果为“复制”
        },
        // 释放文件时触发（核心逻辑）
        handleDrop(e) {
            e.preventDefault();
            this.isDragging = false;
            this.error = '';

            // 获取拖放的文件列表（可能包含多个文件）
            const files = e.dataTransfer.files;
            if (files.length === 0) {
                this.error = '未检测到拖放的文件';
                return;
            }

            // 取第一个文件（假设只处理单张图片）
            const file = files[0];
            if (!file.type.startsWith('image/')) {
                this.error = '错误：仅支持图片文件';
                return;
            }

            // 生成预览 URL（这里使用 Blob URL，也可以替换为 Data URL）
            this.previewUrl = URL.createObjectURL(file);
        },
        // 清除预览
        clearPreview() {
            if (this.previewUrl) {
                URL.revokeObjectURL(this.previewUrl); // 释放内存
                this.previewUrl = null;
            }
            this.error = '';
        }
    },
    beforeUnmount() {
        // 组件卸载时释放资源
        this.clearPreview();
    }
};
</script>

<style scoped>
.drag-drop-container {
    max-width: 600px;
    margin: 20px auto;
    padding: 20px;
}

.drop-zone {
    height: 200px;
    border: 2px dashed #e5e7eb;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.drop-zone:hover {
    border-color: #9ca3af;
}

.drop-zone.active {
    /* 拖入时的高亮样式 */
    border-color: #2563eb;
    background: #f8fafc;
}

.tip {
    color: #64748b;
    font-size: 16px;
    text-align: center;
    line-height: 1.5;
}

.tip.active {
    color: #2563eb;
    font-weight: 500;
}

.preview-area {
    margin-top: 20px;
    text-align: center;
}

.preview-img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.clear-btn {
    display: block;
    margin: 15px auto 0;
    padding: 6px 12px;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.clear-btn:hover {
    background: #dc2626;
}

.error-tip {
    color: #dc2626;
    font-size: 14px;
    margin: 10px 0;
    text-align: center;
}
</style>
