# 通用IDE和编辑器配置
.idea/
*.iml
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 操作系统生成文件
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 依赖目录
node_modules/
jspm_packages/
bower_components/

# 构建产物目录
dist/
build/
out/
target/
*.jar
*.war
*.ear

# Vue/NPM相关
.env.local
.env.*.local
*.js.map
*.css.map
*.stats.html

# Java相关
*.class
*.ctxt
.mtj.tmp/
*.jar
*.war
*.ear
.hs_err_pid*

# Gradle
.gradle/
build/

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties

# 单元测试报告
TEST*.xml
coverage/
*.jks
*.keystore

# 调试文件
debug.log

# 缓存文件
.cache/
.temp/

# 临时文件
*.tmp
*.bak