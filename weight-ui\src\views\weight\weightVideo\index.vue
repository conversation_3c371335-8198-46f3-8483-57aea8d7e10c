<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="110px">
        <el-form-item label="过磅视频文件名" prop="weightVideoName">
            <el-input v-model="queryParams.weightVideoName" placeholder="请输入过磅视频文件名" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['weight:weightVideo:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['weight:weightVideo:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['weight:weightVideo:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['weight:weightVideo:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="weightVideoList" @selection-change="handleSelectionChange" border stripe highlight-current-row style="width: 100%" :header-cell-style="{background:'#f5f7fa',color:'#606266'}" :cell-style="{padding:'12px 0'}">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="过磅视频ID" align="center" prop="weightVideoId" v-if="false" />
        <el-table-column label="称重记录编号" align="center" prop="weightId" min-width="120" />
        <el-table-column label="过磅视频路径" align="center" prop="weightVideoPath" min-width="180" show-overflow-tooltip />
        <el-table-column label="过磅视频文件名" align="center" prop="weightVideoName" min-width="150" />
        <el-table-column label="操作" align="center" width="180" fixed="right">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['weight:weightVideo:edit']" style="color:#67C23A">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['weight:weightVideo:remove']" style="color:#F56C6C">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改过磅视频对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="110px">
            <el-form-item label="称重记录编码" prop="weightId">
                <el-select v-model="form.weightId" filterable clearable placeholder="请选择称重记录编码" style="width: 100%;">
                    <el-option v-for="item in weightList" :key="item.weightId" :label="item.weightCode" :value="item.weightId">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="过磅视频路径" prop="weightVideoPath">
                <el-input v-model="form.weightVideoPath" placeholder="请输入过磅视频路径" />
            </el-form-item>
            <el-form-item label="过磅视频文件名" prop="weightVideoName">
                <el-input v-model="form.weightVideoName" placeholder="请输入过磅视频文件名" />
            </el-form-item>
            <el-form-item label="备用1" prop="spare1" v-if="false">
                <el-input v-model="form.spare1" placeholder="请输入备用1" />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listWeightVideo,
    getWeightVideo,
    delWeightVideo,
    addWeightVideo,
    updateWeightVideo
} from "@/api/weight/weightVideo";
import {
    listWeight,
} from "@/api/weight/weight";

export default {
    name: "WeightVideo",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 过磅视频表格数据
            weightVideoList: [],
            //称重列表
            weightList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                weightId: null,
                weightVideoName: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {}
        };
    },
    created() {
        this.getList();
        this.getWeighbridgeList();
    },
    methods: {
        /** 查询过磅视频列表 */
        getList() {
            this.loading = true;
            listWeightVideo(this.queryParams).then(response => {
                this.weightVideoList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        //称重列表查询
        async getWeighbridgeList() {
            try {
                const response = await listWeight();
                this.weightList = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                weightVideoId: null,
                weightId: null,
                weightVideoPath: null,
                weightVideoName: null,
                spare1: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.weightVideoId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加过磅视频";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const weightVideoId = row.weightVideoId || this.ids
            getWeightVideo(weightVideoId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改过磅视频";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.weightVideoId != null) {
                        updateWeightVideo(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addWeightVideo(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const weightVideoIds = row.weightVideoId || this.ids;
            this.$modal.confirm('是否确认删除过磅视频编号为"' + weightVideoIds + '"的数据项？').then(function () {
                return delWeightVideo(weightVideoIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('weight/weightVideo/export', {
                ...this.queryParams
            }, `weightVideo_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>

<style scoped>

</style>
