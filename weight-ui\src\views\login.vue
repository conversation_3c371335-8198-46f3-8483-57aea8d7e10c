<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
<div class="min-h-screen flex">
    <div class="loading" v-if="showLoading"> <dv-loading>Loading...</dv-loading> </div>
    <!-- 左侧品牌展示区 -->
    <div class="hidden lg:flex lg:w-3/5 relative">
        <div class="absolute inset-0">
            <img src="../assets/images/info.jpg" class="w-full h-full object-cover object-top" alt="现场场景" />
            <div class="absolute inset-0 bg-gradient-to-r from-blue-900 via-blue-800 to-blue-600 opacity-50"></div>
        </div>
        <div class="relative z-10 flex flex-col justify-between p-12 text-white">
            <div>
                <h1 class="text-4xl font-bold mb-4">华星称重管理系统</h1>
                <p class="text-xl opacity-90">Huaxing Weighing Management System</p>
            </div>
            <div class="space-y-6">
                <h2 class="text-3xl font-medium leading-relaxed">
                    让企业称重管理更简单<br />提升效率，创造价值
                </h2>
                <p class="text-lg opacity-80">
                    打造全方位的企业级称重管理解决方案，助力企业数字化转型升级
                </p>
            </div>
        </div>
    </div>

    <!-- 右侧登录区域 -->
    <div class="w-full lg:w-2/5 flex items-center justify-center p-8 bg-gray-50" v-loading="loadinglogin" element-loading-spinner="" element-loading-text="服务启动中，请稍后..." element-loading-background="rgba(0, 0, 0, 0.8)">
        <div class="w-full max-w-md space-y-8">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">欢迎登录</h2>
                <p class="text-gray-600">请输入您的账号和密码</p>
            </div>

            <form class="space-y-6" @submit.prevent="handleLogin">
                <!-- 账号输入框 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">账号</label>
                    <div class="relative">
                        <input id="username" v-model="form.username" type="text" class="w-full px-4 py-3 border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" :class="{'border-red-500': errors.username}" placeholder="请输入邮箱或手机号" />
                        <i class="fas fa-user absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <p v-if="errors.username" class="mt-1 text-sm text-red-500">{{ errors.username }}</p>
                </div>

                <!-- 密码输入框 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                    <div class="relative">
                        <input id="password" v-model="form.password" :type="showPassword ? 'text' : 'password'" class="w-full px-4 py-3 border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" :class="{'border-red-500': errors.password}" placeholder="请输入密码" />
                        <button type="button" class="!rounded-button absolute right-3 top-1/4 -translate-y-1/2 text-gray-400 hover:text-gray-600" @click="togglePassword">
                            <i :class="showPassword ? 'el-icon-view' : 'el-icon-view'"></i>
                        </button>
                    </div>
                    <p v-if="errors.password" class="mt-1 text-sm text-red-500">{{ errors.password }}</p>
                </div>

                <!-- 验证码 -->
                <div v-if="captchaEnabled">
                    <label class="block text-sm font-medium text-gray-700 mb-2">验证码</label>
                    <div class="flex space-x-4">
                        <div class="relative flex-1">
                            <input id="code" v-model="form.code" type="text" class="w-full px-4 py-3 border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" :class="{'border-red-500': errors.code}" placeholder="请输入验证码" />
                        </div>
                        <img :src="captchaUrl" @click="getCode" class="h-12 w-32 rounded cursor-pointer" alt="验证码" />
                    </div>
                    <p v-if="errors.code" class="mt-1 text-sm text-red-500">{{ errors.code }}</p>
                </div>

                <!-- 记住密码 -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input v-model="form.rememberMe" type="checkbox" class="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500" />
                        <span class="ml-2 text-sm text-gray-600">记住密码</span>
                    </label>
                    <!-- <a href="#" class="text-sm text-blue-600 hover:text-blue-800">忘记密码？</a> -->
                </div>

                <!-- 登录按钮 -->
                <button id="loginBtn" type="submit" class="!rounded-button whitespace-nowrap w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed" :disabled="loading">
                    <i v-if="loading" class="fas fa-circle-notch fa-spin mr-2"></i>
                    {{ loading ? '登录中...' : '登录' }}
                </button>

                <!-- 其他登录方式 -->
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        还没有账号？
                        <a href="/register" class="text-blue-600 hover:text-blue-800">立即注册</a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</div>
</template>

<script>
import {
    getCodeImg,
    getloginChick,
} from "@/api/login";
import Cookies from "js-cookie";
import {
    encrypt,
    decrypt
} from '@/utils/jsencrypt'
export default {
    name: 'LoginPage',
    data() {
        return {
            form: {
                username: '',
                password: '',
                code: '',
                rememberMe: false,
                uuid: ''
            },
            errors: {
                username: '',
                password: '',
                code: '',
                uuid: ''
            },
            showPassword: false,
            loading: false,
            showLoading: false,
            captchaUrl: 'https://ai-public.mastergo.com/ai/img_res/dc5734858cc7c70a59d238ed4b8766eb.jpg',
            loadinglogin: true,
            captchaEnabled: true,
        };
    },
    watch: {
        $route: {
            handler: function (route) {
                this.redirect = route.query && route.query.redirect;
            },
            immediate: true
        }
    },
    created() {
        this.getloginChick()
    },
    methods: {
        getCode() {
            getCodeImg().then(res => {
                this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
                if (this.captchaEnabled) {
                    this.captchaUrl = "data:image/gif;base64," + res.img;
                    this.form.uuid = res.uuid;
                }
            });
        },
        getCookie() {
            const username = Cookies.get("username");
            const password = Cookies.get("password");
            const rememberMe = Cookies.get('rememberMe')
            this.form = {
                username: username === undefined ? this.form.username : username,
                password: password === undefined ? this.form.password : decrypt(password),
                rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
            };
        },
        togglePassword() {
            this.showPassword = !this.showPassword;
        },
        refreshCaptcha() {
            // 刷新验证码
            this.captchaUrl = `${this.captchaUrl}&t=${Date.now()}`;
        },
        validateForm() {
            let isValid = true;
            this.errors = {
                username: '',
                password: '',
                code: ''
            };
            if (!this.form.username) {
                this.errors.username = '请输入账号';
                isValid = false;
            }

            if (!this.form.password) {
                this.errors.password = '请输入密码';
                isValid = false;
            }

            if (!this.form.code && this.captchaEnabled) {
                this.errors.code = '请输入验证码';
                isValid = false;
            }

            return isValid;
        },
        async handleLogin() {
            if (!this.validateForm()) return;

            this.loading = true;
            // 登录请求
            if (this.form.rememberMe) {
                Cookies.set("username", this.form.username, {
                    expires: 30
                });
                Cookies.set("password", encrypt(this.form.password), {
                    expires: 30
                });
                Cookies.set('rememberMe', this.form.rememberMe, {
                    expires: 30
                });
            } else {
                Cookies.remove("username");
                Cookies.remove("password");
                Cookies.remove('rememberMe');
            }
            this.$store.dispatch("Login", this.form).then(() => {
                
                                // 添加延迟确保状态完全更新
                this.$nextTick(() => {
                    let targetPath = this.redirect || "/";

                    // 在Electron环境中，确保使用正确的首页路径
                    if ((window.electronAPI || window.require) && targetPath === "/") {
                        targetPath = "/index";  // 确保使用正确的首页路径
                    }
                    
                                        // 在Electron环境中，尝试使用不同的跳转策略
                    if (window.electronAPI || window.require) {
                        // 强制刷新路由
                        setTimeout(() => {
                            // 尝试多种跳转方法
                            this.$router.replace(targetPath).then(() => {
                                // 跳转成功
                            }).catch((error) => {
                                this.$router.push(targetPath).then(() => {
                                    // 跳转成功
                                }).catch((pushError) => {
                                    // 最后的备选方案：直接设置location
                                    try {
                                        if (this.$router.mode === 'hash') {
                                            const newHash = '#' + targetPath;
                                            window.location.hash = newHash;
                                        } else {
                                            window.location.href = targetPath;
                                        }
                                    } catch (locationError) {
                                        console.error('设置location失败:', locationError);
                                    }
                                });
                            });
                        }, 100);
                    } else {
                        // 浏览器环境使用正常跳转
                        this.$router.push({
                            path: targetPath
                        }).catch((error) => {
                            // 如果push失败，尝试使用replace
                            this.$router.replace({
                                path: targetPath
                            }).catch(err => console.error('replace也失败:', err));
                        });
                    }
                });
            }).catch(() => {
                this.loading = false;
                if (this.captchaEnabled) {
                    this.getCode();
                }
            });
        },
        getloginChick() {
            const POLL_INTERVAL = 3000;
            let timerId = null;
            const checkStatus = async () => {
                try {
                    const res = await getloginChick();
                    if (res.code == "200") {
                        clearTimeout(timerId);
                        this.getCode();
                        this.getCookie();
                        this.loadinglogin = false;
                        return;
                    }
                } catch (error) {
                    console.error('Polling error:', error);
                }
                timerId = setTimeout(checkStatus, POLL_INTERVAL);
            };
            timerId = setTimeout(checkStatus, POLL_INTERVAL);
            // 组件卸载时清除定时器
            this.$once('hook:beforeDestroy', () => {
                clearTimeout(timerId);
            });
        },
    }
};
</script>

<style scoped>
.login-page-input::-webkit-outer-spin-button,
.login-page-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.loading {
    @apply bg-gray-800 fixed top-0 left-0 w-full h-full opacity-80 z-50 text-white;
}
::v-deep .el-loading-spinner {
    left: 50% !important;
}
::v-deep .el-loading-text {
    translate: -45% 0%;
}
</style>
