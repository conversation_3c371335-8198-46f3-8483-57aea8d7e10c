@echo off
echo 正在修复Electron构建问题...

echo 1. 清理electron-builder缓存...
if exist "%LOCALAPPDATA%\electron-builder\Cache" (
    rmdir /s /q "%LOCALAPPDATA%\electron-builder\Cache"
    echo 缓存已清理
) else (
    echo 缓存目录不存在，跳过清理
)

echo 2. 清理npm缓存...
npm cache clean --force

echo 3. 清理node_modules...
if exist "node_modules" (
    rmdir /s /q "node_modules"
    echo node_modules已清理
)

echo 4. 重新安装依赖...
npm install

echo 5. 开始构建...
npm run electron:build-win

echo 构建完成！
pause