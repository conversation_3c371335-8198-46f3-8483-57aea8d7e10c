import request from '@/utils/request'

// 查询地磅列表
export function listWeighbridge(query) {
  return request({
    url: '/system/weighbridge/list',
    method: 'get',
    params: query
  })
}

// 查询地磅详细
export function getWeighbridge(weighbridgeId) {
  return request({
    url: '/system/weighbridge/' + weighbridgeId,
    method: 'get'
  })
}

// 新增地磅
export function addWeighbridge(data) {
  return request({
    url: '/system/weighbridge',
    method: 'post',
    data: data
  })
}

// 修改地磅
export function updateWeighbridge(data) {
  return request({
    url: '/system/weighbridge',
    method: 'put',
    data: data
  })
}

// 删除地磅
export function delWeighbridge(weighbridgeId) {
  return request({
    url: '/system/weighbridge/' + weighbridgeId,
    method: 'delete'
  })
}
