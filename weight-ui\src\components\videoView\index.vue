<template>
<el-dialog title="过 磅 视 频" :visible.sync="open" :close-on-click-modal="false" width="70%" class="img-eel-dialog" append-to-body :before-close="cancel">
    <div>
        <div v-for="(src, idx) in arr" :key="idx" style="margin-bottom: 20px;">
            <video :src="src" width="100%" height="400" controls></video>
        </div>
    </div>
</el-dialog>
</template>

<script>
export default {
    name: "tcvideo",
    data() {
        return {
            open: false,
            arr: [],
        };
    },
    methods: {
        //开启弹窗
        openDialog(videoArr, httpUrl) {
            console.log("查看过磅视频");
            // videoArr 支持数组或字符串
            if (Array.isArray(videoArr)) {
                this.arr = videoArr.map(item => 'http://' + httpUrl + "/weight/weight/getVideo?videoUrl=" + String(item).replace(/\\/g, '/'));
            } else if (videoArr) {
                this.arr = ['http://' + httpUrl + "/weight/weight/getVideo?videoUrl=" + String(videoArr).replace(/\\/g, '/')];
            } else {
                this.arr = [];
            }
            this.open = true;
            console.log(this.arr);
        },
        cancel() {
            this.$showPlugin()
            this.open = false;
            this.arr = [];
        },
    },
};
</script>

<style scoped>
.img-eel-dialog>>>.el-dialog {
    margin-top: 0 !important;
}

.img-eel-dialog>>>.el-dialog__header {
    background-color: #1d6498;
    padding: 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.img-eel-dialog>>>.el-dialog__title {
    color: #f3f4f5;
    font-size: 2.5rem;
    font-weight: 900;
}

.img-eel-dialog>>>.el-dialog__headerbtn {
    width: 2.5rem;
    height: 2.5rem;
    top: 10px;
}

.img-eel-dialog>>>.el-dialog__headerbtn .el-dialog__close {
    line-height: 3.4rem;
    font-size: 2.5rem;
    color: #f3f4f5;
}

.el-dialog__body {
    padding: 15px 20px;
}

.dialog-footer {
    width: 100%;
    height: 1px;
    display: flex;
    justify-content: center;
}

.img {
    width: 100%;
    /* height: 50%; */
    border-radius: 0.05rem;
    padding: 0.05rem;
    background-color: #4791c7;
    z-index: 100;
    overflow: hidden;
}

.img>>>img {
    width: 100%;
    height: 100%;
}

ul {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

ul li {
    width: 50%;
    height: 50%;
    list-style: none;
    padding: 0.03rem;
}
</style>
