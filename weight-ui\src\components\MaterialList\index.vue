<template>
  <div class="material-list">
    <el-card shadow="always" class="main-card">
      <div slot="header" class="card-header">
        <span class="card-title">物料列表</span>
        <div class="btn-group">
          <el-button type="primary" icon="el-icon-plus" v-hasPermi="['basicInfo:material:add']" size="mini" @click="openDialog('add')">新增</el-button>
          <el-button type="danger" icon="el-icon-delete" v-hasPermi="['basicInfo:material:remove']" size="mini" :disabled="!selection.length" @click="handleBatchDelete">批量删除</el-button>
          <el-button type="info" icon="el-icon-upload2" v-hasPermi="['basicInfo:material:export']" size="mini" @click="handleImport">导入</el-button>
          <el-button type="warning" icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
        </div>
      </div>
      
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="search-form" label-width="68px">
        <el-form-item label="物料编码" prop="materialCode">
          <el-input v-model="queryParams.materialCode" placeholder="请输入物料编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态">
            <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 表格 -->
      <el-table 
        :data="list" 
        highlight-current-row 
        @current-change="handleRowClick" 
        :loading="loading" 
        :row-key="row => row.materialId || row.id || row.materialCode" 
        ref="table" 
        class="main-table" 
        :header-cell-style="headerCellStyle" 
        :cell-style="cellStyle" 
        empty-text="暂无物料" 
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column prop="materialCode" label="物料编码" min-width="80" />
        <el-table-column prop="materialName" label="物料名称" min-width="80" />
        <el-table-column prop="categoryName" label="物料类别" min-width="90" v-if="false" />
        <el-table-column prop="status" label="启用状态" min-width="80">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" active-value="Y" inactive-value="N" @change="handleStatusChange(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini" v-hasPermi="['basicInfo:material:edit']" @click="openDialog('edit', scope.row)">编辑</el-button>
            <el-button type="text" size="mini" v-hasPermi="['basicInfo:material:remove']" style="color:#F56C6C" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 分页 -->
    <pagination 
      v-show="total > 0" 
      :total="total" 
      :page.sync="queryParams.pageNum" 
      :limit.sync="queryParams.pageSize" 
      @pagination="getList" 
      class="card-pagination" 
    />

    <!-- 编辑弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="420px" :close-on-click-modal="false" class="custom-dialog">
      <el-form :model="form" :rules="rules" ref="form" label-width="90px">
        <el-form-item label="物料编码" prop="materialCode">
          <el-input v-model="form.materialCode" placeholder="请输入物料编码" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入物料名称" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="物料类别" prop="categoryId" v-if="false">
          <el-select v-model="form.categoryId" placeholder="请选择物料类别" filterable>
            <el-option v-for="item in categoryList" :key="item.categoryId" :label="item.categoryName" :value="item.categoryId" />
          </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%;">
            <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="dialogLoading" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload 
        ref="upload" 
        :limit="1" 
        accept=".xlsx, .xls" 
        :headers="upload.headers" 
        :action="upload.url + '?updateSupport=' + upload.updateSupport" 
        :disabled="upload.isUploading" 
        :on-progress="handleFileUploadProgress(upload)" 
        :on-success="handleFileUploadSuccess(upload, 'upload', getList)" 
        :auto-upload="false" 
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的物料数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="downloadTemplate('basicInfo/material/importTemplate', {}, `material_template_${new Date().getTime()}.xlsx`)">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFile('upload')">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMaterial,
  getMaterial,
  addMaterial,
  updateMaterial,
  delMaterial
} from "@/api/basicInfo/material";
import {
  listCategory
} from "@/api/basicInfo/category";
import tableCommon from "@/mixins/tableCommon";
import importCommon from "@/mixins/importCommon";

export default {
  name: "MaterialList",
  dicts: ['sys_yes_no'],
  mixins: [tableCommon, importCommon],
  props: {
    // 外部可以传入初始查询参数
    initialQueryParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      currentRow: null,
      dialogVisible: false,
      dialogTitle: "",
      dialogType: "add",
      dialogLoading: false,
      form: {
        materialCode: "",
        materialName: "",
        categoryId: null,
        status: 'Y',
      },
      rules: {
        materialCode: [{
          required: true,
          message: "物料编码不能为空",
          trigger: "blur"
        }],
        materialName: [{
          required: true,
          message: "物料名称不能为空",
          trigger: "blur"
        }],
        categoryId: [{
          required: true,
          message: "请选择物料类别",
          trigger: "change"
        }],
        status: [{
          required: true,
          message: "请选择启用状态",
          trigger: "change"
        }]
      },
      categoryList: [],
      selection: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialCode: '',
        materialName: '',
        status: 'Y',
        ...this.initialQueryParams
      },
      total: 0,
      upload: this.createImportConfig("/basicInfo/material/importData")
    };
  },
  created() {
    this.getCategoryList();
    this.getList();
  },
  methods: {
    // 获取类别列表
    getCategoryList() {
      listCategory().then(res => {
        this.categoryList = res.rows;
      });
    },
    
    // 物料状态修改
    handleStatusChange(row) {
      let text = row.status === "Y" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.materialName + '"物料吗？').then(() => {
        return updateMaterial(row);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(() => {
        row.status = row.status === "Y" ? "N" : "Y";
      });
    },
    
    // 获取物料列表
    getList() {
      this.loading = true;
      listMaterial({ ...this.queryParams }).then(res => {
        this.list = res.rows;
        this.total = res.total;
        this.loading = false;
        
        // 自动选中第一行
        if (this.list.length) {
          this.$nextTick(() => {
            this.$refs.table.setCurrentRow(this.list[0]);
            this.handleRowClick(this.list[0]);
          });
        } else {
          this.currentRow = null;
          this.$emit('material-change', null);
        }
      });
    },
    
    // 行点击事件
    handleRowClick(row) {
      this.currentRow = row;
      this.$emit('material-change', row);
    },
    
    // 打开对话框
    async openDialog(type, row) {
      this.dialogType = type;
      if (type === "add") {
        const res = await this.$getAutoCode();
        this.dialogTitle = "新增物料";
        this.form = {
          materialCode: res.msg,
          materialName: "",
          categoryId: null,
          status: 'Y',
        };
      } else if (type === "edit") {
        const target = row || this.currentRow;
        if (!target) return;
        this.dialogTitle = "编辑物料";
        this.form = { ...target };
      }
      this.dialogVisible = true;
    },
    
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (!valid) return;
        this.dialogLoading = true;
        
        const api = this.dialogType === "add" ? addMaterial : updateMaterial;
        api(this.form).then(() => {
          this.$message.success(this.dialogType === "add" ? "新增成功" : "修改成功");
          this.dialogVisible = false;
          this.getList();
        }).finally(() => {
          this.dialogLoading = false;
        });
      });
    },
    
    // 删除
    handleDelete(row) {
      const target = row || this.currentRow;
      if (!target) return;
      this.confirmDelete(target.materialName, () => {
        return delMaterial(target.materialId).then(() => {
          this.$message.success("删除成功");
          this.getList();
        });
      });
    },
    
    // 批量删除
    handleBatchDelete() {
      if (!this.selection.length) return;
      this.confirmBatchDelete(() => {
        return delMaterial(this.selection.map(i => i.materialId)).then(() => {
          this.$message.success("批量删除成功");
          this.getList();
        });
      });
    },
    
    // 选择变化
    handleSelectionChange(val) {
      this.selection = val;
    },
    
    // 导出
    handleExport() {
      this.download && this.download('basicInfo/material/export', this.queryParams, `material_${new Date().getTime()}.xlsx`);
    },
    
    // 导入
    handleImport() {
      this.upload.title = "物料导入";
      this.upload.open = true;
    },
    
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    // 重置
    resetQuery() {
      this.resetQueryParams('queryForm', this.queryParams);
      this.getList();
    }
  }
};
</script>

<style scoped>
.material-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.main-card {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
  padding: 16px 10px 16px 10px;
  margin: 0;
  height: 100%;
  overflow: hidden;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: 50px;
}

.card-header,
.search-form,
.btn-group {
  flex-shrink: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
  margin-bottom: 8px;
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: #222;
}

.btn-group {
  display: flex;
  align-items: center;
  margin-left: auto;
  justify-content: flex-end;
}

.btn-group .el-button {
  margin-left: 4px;
  padding: 5px 8px;
  font-size: 12px;
}

.search-form {
  margin-bottom: 8px;
  width: 100%;
}

.search-form >>>.el-form-item {
  margin-bottom: 8px;
}

.search-form >>>.el-form-item__label {
  font-size: 12px;
  width: 60px !important;
}

.search-form >>>.el-input {
  width: 100px;
}

.search-form >>>.el-select {
  width: 100px;
}

.main-table {
  flex: 1 1 0;
  min-height: 0;
  border-radius: 8px;
  background: #fff;
  margin-top: 0;
}

::v-deep .el-table {
  height: 70vh !important;
  display: flex;
  flex-direction: column;
}

.el-table::before {
  height: 0;
}

::v-deep .el-table__header-wrapper,
::v-deep .el-table__footer-wrapper {
  flex-shrink: 0;
}

::v-deep .el-table__body-wrapper {
  min-height: 0;
  height: calc(100% - 140px);
  overflow-y: auto !important;
  max-height: 100% !important;
}

.custom-dialog>>>.el-dialog__header {
  border-radius: 10px 10px 0 0;
  background: #f5f7fa;
  padding: 16px 20px;
}

.custom-dialog>>>.el-dialog__title {
  font-size: 16px;
  font-weight: bold;
}

.custom-dialog>>>.el-dialog__body {
  padding-top: 18px;
  padding-bottom: 0;
}

.dialog-footer {
  text-align: right;
  padding: 10px 0 0 0;
}

.card-pagination {
  margin: 12px auto 0 auto;
  text-align: center;
  width: 100%;
  position: static;
  padding: 0;
  background: none;
  box-shadow: none;
  z-index: auto;
}
</style>