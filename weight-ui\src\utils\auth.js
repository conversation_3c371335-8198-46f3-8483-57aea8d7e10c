import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

// Always prefer localStorage if available; fallback to Cookies
export function getToken() {
  try {
    const ls = typeof window !== 'undefined' ? window.localStorage : null
    if (ls) {
      const token = ls.getItem(TokenKey)
      if (token) return token
    }
  } catch (_) {}
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  try {
    const ls = typeof window !== 'undefined' ? window.localStorage : null
    if (ls) ls.setItem(TokenKey, token)
  } catch (_) {}
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  try {
    const ls = typeof window !== 'undefined' ? window.localStorage : null
    if (ls) ls.removeItem(TokenKey)
  } catch (_) {}
  return Cookies.remove(TokenKey)
}
