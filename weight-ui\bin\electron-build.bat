@echo off
echo 开始构建桌面应用程序...
echo 这将先构建Vue项目，然后打包成Windows桌面应用

REM 设置环境变量
set NODE_ENV=production

REM 清理旧的构建文件
if exist dist rmdir /s /q dist
if exist dist_electron rmdir /s /q dist_electron

echo 正在构建Vue项目...
call npm run build:prod

if %errorlevel% neq 0 (
    echo Vue项目构建失败！
    pause
    exit /b %errorlevel%
)

echo Vue项目构建成功！
echo 正在打包Electron应用...
call npm run electron:build-win

if %errorlevel% neq 0 (
    echo Electron应用打包失败！
    pause
    exit /b %errorlevel%
)

echo 桌面应用构建完成！
echo 安装包位置：dist_electron/
pause