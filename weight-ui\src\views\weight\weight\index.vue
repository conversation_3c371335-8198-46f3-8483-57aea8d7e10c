<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="磅单编码" prop="weightCode">
            <el-input v-model="queryParams.weightCode" placeholder="请输入磅单编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="磅单类型" prop="weightType">
            <el-select v-model="queryParams.weightType" placeholder="请选择磅单类型" clearable>
                <el-option v-for="dict in dict.type.business_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="客户名称" prop="customerId">
            <el-select v-model="queryParams.customerId" placeholder="请选择客户名称" filterable clearable>
                <el-option v-for="dict in customerOptions" :key="dict.customerId" :label="dict.customerName" :value="dict.customerId" />
            </el-select>
        </el-form-item>
        <el-form-item label="供应商名" prop="supplierId">
            <el-select v-model="queryParams.supplierId" placeholder="请选择供应商名" filterable clearable>
                <el-option v-for="dict in supplierOptions" :key="dict.supplierId" :label="dict.supplierName" :value="dict.supplierId" />
            </el-select>
        </el-form-item>
        <el-form-item label="物料类别" prop="categoryId" v-if="false">
            <el-select v-model="queryParams.categoryId" placeholder="请选择物料类别" clearable>
                <el-option v-for="dict in dict.type.driver_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="物料名称" prop="materialId">
            <el-select v-model="queryParams.materialId" filterable placeholder="请选择物料名称" clearable>
                <el-option v-for="dict in goodsNameOptions" :key="dict.materialId" :label="dict.materialName" :value="dict.materialId" />
            </el-select>
        </el-form-item>
        <el-form-item label="规格名称" prop="specId">
            <el-select v-model="queryParams.specId" filterable placeholder="请选择规格名称" clearable>
                <el-option v-for="dict in specificationNameOptions" :key="dict.specId" :label="dict.specName" :value="dict.specId" />
            </el-select>
        </el-form-item>
        <el-form-item label="司机名称" prop="driverId" v-if="false">
            <el-select v-model="queryParams.driverId" placeholder="请选择司机名称" clearable>
                <el-option v-for="dict in dict.type.driver_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="车牌号码" prop="vehicleId">
            <el-select v-model="queryParams.vehicleId" filterable placeholder="请选择车牌号码" clearable>
                <el-option v-for="dict in licensePlateOptions" :key="dict.vehicleId" :label="dict.licensePlate" :value="dict.vehicleId" />
            </el-select>
        </el-form-item>
        <el-form-item label="磅单状态" prop="billStatus">
            <el-select v-model="queryParams.billStatus" placeholder="请选择磅单状态" clearable>
                <el-option v-for="dict in dict.type.pound_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="上传状态" prop="uploadStatus">
            <el-select v-model="queryParams.uploadStatus" placeholder="请选择上传状态" clearable>
                <el-option v-for="dict in dict.type.upload_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="作废状态" prop="nullifyStatus">
            <el-select v-model="queryParams.nullifyStatus" placeholder="请选择作废状态" clearable>
                <el-option v-for="dict in dict.type.invalid_state" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="时间范围" prop="dateRange">
            <el-date-picker
                v-model="queryParams.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                clearable
                @change="handleDateRangeChange"
                @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['weight:weight:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['weight:weight:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['weight:weight:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['weight:weight:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="weightList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="磅单ID" align="center" prop="weightId" v-if="false" />
        <el-table-column label="预约单ID" align="center" prop="orderId" v-if="false" />
        <el-table-column label="磅单编码" align="center" prop="weightCode" />
        <el-table-column label="磅单类型" align="center" prop="weightType">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.business_type" :value="scope.row.weightType" />
            </template>
        </el-table-column>
        <el-table-column label="车牌号码" align="center" prop="licensePlate"></el-table-column>
        <el-table-column label="客户名称" align="center" prop="customerName"></el-table-column>
        <el-table-column label="供应商名" align="center" prop="supplierName"></el-table-column>
        <el-table-column label="物料类别" align="center" prop="categoryId" v-if="false">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.driver_type" :value="scope.row.categoryId" />
            </template>
        </el-table-column>
        <el-table-column label="物料名称" align="center" prop="materialName"></el-table-column>
        <el-table-column label="规格名称" align="center" prop="specName"></el-table-column>
        <el-table-column label="司机名称" align="center" prop="driverId" v-if="false">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.driver_type" :value="scope.row.driverId" />
            </template>
        </el-table-column>
        <el-table-column label="车队名称" align="center" prop="fleetId" v-if="false">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.driver_type" :value="scope.row.fleetId" />
            </template>
        </el-table-column>
        <el-table-column label="毛重" align="center" prop="weightM" />
        <el-table-column label="皮重" align="center" prop="weightP" />
        <el-table-column label="净重" align="center" prop="netWeight" />
        <el-table-column label="实重" align="center" prop="actualWeight" />
        <el-table-column label="扣率" align="center" prop="cutPer"/>
        <el-table-column label="扣重" align="center" prop="cutWeight" />
        <el-table-column label="一次过磅地磅编号" align="center" prop="dbCodeFirst" />
        <el-table-column label="二次过磅地磅编号" align="center" prop="dbCodeSecond" />
        <el-table-column label="一次过磅时间" align="center" prop="weightFirstTime" width="180">
            <template slot-scope="scope">
                <span>{{ parseTime(scope.row.weightFirstTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
        </el-table-column>
        <el-table-column label="二次过磅时间" align="center" prop="weightSecondTime" width="180">
            <template slot-scope="scope">
                <span>{{ parseTime(scope.row.weightSecondTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
            </template>
        </el-table-column>
        <el-table-column label="一次称重过磅员" align="center" prop="weightFirstBy" />
        <el-table-column label="二次称重过磅员" align="center" prop="weightSecondBy" />
        <el-table-column label="扣杂人" align="center" prop="cutBy" v-if="false" />
        <el-table-column label="磅单状态" align="center" prop="billStatus">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.pound_status" :value="scope.row.billStatus" />
            </template>
        </el-table-column>
        <el-table-column label="上传状态" align="center" prop="uploadStatus">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.upload_status" :value="scope.row.uploadStatus" />
            </template>
        </el-table-column>
        <el-table-column label="作废状态" align="center" prop="nullifyStatus">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.invalid_state" :value="scope.row.nullifyStatus" />
            </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['weight:weight:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['weight:weight:remove']">删除</el-button>
                <el-button size="mini" type="text" icon="el-icon-picture-outline" @click="imgViewVisibleFunction(scope.row.weightId)">图片查看</el-button>
                <el-button size="mini" type="text" icon="el-icon-video-camera" @click="videoViewVisibleFunction(scope.row.weightId)">视频查看</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改称重记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body :close-on-click-modal="false">
        <el-form ref="form" :model="form" :rules="rules" label-width="130px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="磅单编码" prop="weightCode" v-if="false">
                        <el-input v-model="form.weightCode" placeholder="请输入磅单编码" />
                    </el-form-item>
                    <el-form-item label="磅单类型" prop="weightType">
                        <el-select v-model="form.weightType" placeholder="请选择磅单类型" style="width:100%">
                            <el-option v-for="dict in dict.type.business_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="客户名称" prop="customerId">
                        <el-select v-model="form.customerId" filterable placeholder="请选择客户名称" style="width:100%">
                            <el-option v-for="dict in customerOptions" :key="dict.customerName" :label="dict.customerName" :value="dict.customerId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="供应商名" prop="supplierId">
                        <el-select v-model="form.supplierId" filterable placeholder="请选择供应商名" style="width:100%">
                            <el-option v-for="dict in supplierOptions" :key="dict.supplierName" :label="dict.supplierName" :value="dict.supplierId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="物料类别" prop="categoryId" v-if="false">
                        <el-select v-model="form.categoryId" filterable placeholder="请选择物料类别" style="width:100%">
                            <el-option v-for="dict in dict.type.driver_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="物料名称" prop="materialId">
                        <el-select v-model="form.materialId" filterable placeholder="请选择物料名称" style="width:100%">
                            <el-option v-for="dict in goodsNameOptions" :key="dict.materialId" :label="dict.materialName" :value="dict.materialId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="规格名称" prop="specId" v-if="false">
                        <el-select v-model="form.specId" filterable placeholder="请选择规格名称" style="width:100%">
                            <el-option v-for="dict in specificationNameOptions" :key="dict.specId" :label="dict.specName" :value="dict.specId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="司机名称" prop="driverId" v-if="false">
                        <el-select v-model="form.driverId" placeholder="请选择司机名称" style="width:100%">
                            <el-option v-for="dict in dict.type.driver_type" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="车队名称" prop="fleetId" v-if="false">
                        <el-input v-model="form.fleetId" placeholder="请输入车队名称" />
                    </el-form-item>
                    <el-form-item label="车牌号码" prop="vehicleId">
                        <el-select v-model="form.vehicleId" filterable placeholder="请选择车牌号码" style="width:100%">
                            <el-option v-for="dict in licensePlateOptions" :key="dict.licensePlate" :label="dict.licensePlate" :value="dict.vehicleId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="磅单状态" prop="billStatus">
                        <el-select v-model="form.billStatus" placeholder="请选择磅单状态" style="width:100%">
                            <el-option v-for="dict in dict.type.pound_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="一次过磅重量" prop="weightFirst">
                        <el-input v-model="form.weightFirst" placeholder="请输入一次过磅重量" />
                    </el-form-item>
                    <el-form-item label="二次过磅重量" prop="weightSecond">
                        <el-input v-model="form.weightSecond" placeholder="请输入二次过磅重量" />
                    </el-form-item>
                    <el-form-item label="净重" prop="netWeight">
                        <el-input v-model="form.netWeight" placeholder="请输入净重" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="一次过磅时间" prop="weightFirstTime">
                        <el-date-picker style="width:100%" clearable v-model="form.weightFirstTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择一次过磅时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="二次过磅时间" prop="weightSecondTime">
                        <el-date-picker style="width:100%" clearable v-model="form.weightSecondTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择二次过磅时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="一次称重过磅员" prop="weightFirstBy">
                        <el-input v-model="form.weightFirstBy" placeholder="请输入一次称重过磅员" />
                    </el-form-item>
                    <el-form-item label="二次称重过磅员" prop="weightSecondBy">
                        <el-input v-model="form.weightSecondBy" placeholder="请输入二次称重过磅员" />
                    </el-form-item>
                    <el-form-item label="实重" prop="actualWeight" v-if="false">
                        <el-input v-model="form.actualWeight" placeholder="请输入实重" />
                    </el-form-item>
                    <el-form-item label="扣率" prop="cutPer" v-if="false">
                        <el-input v-model="form.cutPer" placeholder="请输入扣率" />
                    </el-form-item>
                    <el-form-item label="扣杂人" prop="cutBy" v-if="false">
                        <el-input v-model="form.cutBy" placeholder="请输入扣杂人" />
                    </el-form-item>
                    <el-form-item label="上传状态" prop="uploadStatus">
                        <el-select v-model="form.uploadStatus" placeholder="请选择上传状态" style="width:100%">
                            <el-option v-for="dict in dict.type.upload_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="作废状态" prop="nullifyStatus">
                        <el-select v-model="form.nullifyStatus" placeholder="请选择作废状态" style="width:100%">
                            <el-option v-for="dict in dict.type.invalid_state" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="一次称重地磅编号" prop="dbCodeFirst">
                        <el-input v-model="form.dbCodeFirst" placeholder="请输入一次称重地磅编号" />
                    </el-form-item>
                    <el-form-item label="二次称重地磅编号" prop="dbCodeSecond">
                        <el-input v-model="form.dbCodeSecond" placeholder="请输入二次称重地磅编号" />
                    </el-form-item>
                    <el-form-item label="备注" prop="remark">
                        <el-input v-model="form.remark" placeholder="请输入备注" />
                    </el-form-item>
                    <el-form-item label="备用1" prop="spare1" v-if="false">
                        <el-input v-model="form.spare1" placeholder="请输入备用1" />
                    </el-form-item>
                    <el-form-item label="备用2" prop="spare2" v-if="false">
                        <el-input v-model="form.spare2" placeholder="请输入备用2" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>
    <img-view ref="imgView"></img-view>
    <video-view ref="videoView"></video-view>
</div>
</template>

<script>
import {
    listWeight,
    getWeight,
    delWeight,
    addWeight,
    updateWeight
} from "@/api/weight/weight";
import {
    listCustomer,
} from "@/api/basicInfo/customer";
import {
    listVehicle,
} from "@/api/basicInfo/vehicle";
import {
    listSupplier,
} from "@/api/basicInfo/supplier";
import {
    listMaterial,
} from "@/api/basicInfo/material";
import {
    listSpec,
} from "@/api/basicInfo/spec";
import {
    listWeightImg,
} from "@/api/weight/weightImg";
import {
    listWeightVideo,
} from "@/api/weight/weightVideo";
import imgView from "@/components/imgView";
import videoView from '@/components/videoView/index.vue'

export default {
    name: "Weight",
    dicts: ['driver_type', 'business_type', 'pound_status', 'upload_status', 'invalid_state'],
    components: {
        imgView,
        videoView
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 称重记录表格数据
            weightList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            customerOptions: [], //客户数据
            supplierOptions: [], //供应商数据
            goodsNameOptions: [], //货物名称数据
            licensePlateOptions: [], //车牌号数据
            specificationNameOptions: [], //规格名称数据
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                weightCode: null,
                weightType: null,
                customerId: null,
                supplierId: null,
                categoryId: null,
                materialId: null,
                specId: null,
                driverId: null,
                vehicleId: null,
                billStatus: null,
                uploadStatus: null,
                nullifyStatus: null,
                startDate: null,
                endDate: null,
                dateRange: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
            basicInfoQuery: {
                pageNum: 1,
                pageSize: 999999999,
            },
            viewUrl: '',//视图查看路径
            currentRowId: '',//当前行id
        };
    },
   async created() {
        this.getList();
        this.getBasicInfo();
        //视图查看路径
        const viewUrl = await this.$fetchConfigByKey('view_preview');
        this.viewUrl = viewUrl.msg;
    },
    methods: {
        /** 查询称重记录列表 */
        getList() {
            this.loading = true;
            listWeight(this.queryParams).then(response => {
                this.weightList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        //基础信息查询
        getBasicInfo() {
            this.getCustomer();
            this.getSupplier();
            this.getLicensePlate();
            this.getGoodsName();
            this.getSpecificationName();
        },
        //客户数据查询
        async getCustomer() {
            try {
                const response = await listCustomer(this.basicInfoQuery);
                this.customerOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        //供应商数据查询
        async getSupplier() {
            try {
                const response = await listSupplier(this.basicInfoQuery);
                this.supplierOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        //货物名称数据查询
        async getGoodsName() {
            try {
                const response = await listMaterial(this.basicInfoQuery);
                this.goodsNameOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        //车牌号码数据查询
        async getLicensePlate() {
            try {
                const response = await listVehicle(this.basicInfoQuery);
                this.licensePlateOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
        //规格名称数据查询
        async getSpecificationName(materialId) {
            try {
                const response = await listSpec({
                    ...this.basicInfoQuery
                });
                this.specificationNameOptions = response.rows;
            } catch (error) {
                this.$message.error(error.msg);
            }
        },
                //图片查看
        imgViewVisibleFunction(id) {
            const RowId = this.currentRowId || id;
            if (!RowId) {
                this.$message.warning('请先选择一条数据');
                return;
            }
            const urlList = []
            listWeightImg({
                weightId: RowId
            }).then(response => {
                if (response.rows.length == 0) {
                    this.$message.warning('暂无图片');
                    return;
                }
                response.rows.forEach(item => {
                    urlList.push(item.weightImgPath)
                })
                console.log(this.viewUrl);

                this.$refs.imgView.openDialog(urlList, this.viewUrl);
            });
        },
        //视频查看
        videoViewVisibleFunction(id) {
            const RowId = this.currentRowId || id;
            if (!RowId) {
                this.$message.warning('请先选择一条数据');
                return;
            }
            listWeightVideo({
                weightId: RowId
            }).then(response => {
                if (response.rows.length == 0) {
                    this.$message.warning('暂无视频');
                    return;
                }
                const urlList = response.rows.map(item => item.weightVideoPath);
                this.$refs.videoView.openDialog(urlList, this.viewUrl);
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                weightId: null,
                orderId: null,
                weightCode: null,
                weightType: null,
                customerId: null,
                supplierId: null,
                categoryId: null,
                materialId: null,
                specId: null,
                driverId: null,
                fleetId: null,
                vehicleId: null,
                billStatus: null,
                dbCodeM: null,
                dbCodeP: null,
                weighP: null,
                weighM: null,
                weighPTime: null,
                weighMTime: null,
                weighMBy: null,
                weighPBy: null,
                netWeight: null,
                actualWeight: null,
                cutPer: null,
                cutBy: null,
                uploadStatus: null,
                nullifyStatus: null,
                remark: null,
                spare1: null,
                spare2: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.queryParams.startDate = '';
            this.queryParams.endDate = '';
            this.handleQuery();
        },
        /** 处理日期范围变化 */
        handleDateRangeChange(dateRange) {
            if (dateRange && dateRange.length === 2) {
                this.queryParams.startDate = dateRange[0];
                this.queryParams.endDate = dateRange[1];
            } else {
                this.queryParams.startDate = null;
                this.queryParams.endDate = null;
            }
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.weightId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加称重记录";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const weightId = row.weightId || this.ids
            getWeight(weightId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改称重记录";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.weightId != null) {
                        updateWeight(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addWeight(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const weightIds = row.weightId || this.ids;
            this.$modal.confirm('是否确认删除称重记录编号为"' + weightIds + '"的数据项？').then(function () {
                return delWeight(weightIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('weight/weight/export', {
                ...this.queryParams
            }, `weight_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
