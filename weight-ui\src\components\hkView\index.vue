<template>
<div class="hk-view" style="width:100%;height:100%;">
    <div id="divPlugin" style="width:100%;height:100%;"></div>
</div>
</template>

<script>
export default {
    name: 'hk-view',
    dicts: ['video_address'],
    data() {
        return {
            // 操作信息
            opinfo: '',
            // 窗口索引
            wndIndex: 0,
            // 窗口数量
            wndCount: 2,
            //个设备信息
            devices: [],
            //密码
            password: 'hxcz12345678',
        }
    },
    async mounted() {
        //获取密码
        const data = await this.$fetchConfigByKey('camera_password');
        this.password = data.msg;
        this.devices = []
        if (this.dict && this.dict.type && this.dict.type.video_address && this.dict.type.video_address.length) {
            this.dict.type.video_address.forEach((item, index) => {
                this.devices.push({
                    loginip: item.value,
                    port: '80',
                    username: 'admin',
                    password: this.password,
                    deviceIdentify: '',
                    channels: [],
                    channelId: ''
                })
                // this.devices[index].loginip = item.value
            })
            setTimeout(() => {
                this.getWebVideoCtrl();
            }, 500)
        } else {
            // 监听 dict.type.video_address 的变化，数据准备好后再赋值
            this.$watch(
                () => this.dict && this.dict.type && this.dict.type.video_address,
                (val) => {
                    if (val && val.length) {
                        val.forEach((item, index) => {
                            this.devices.push({
                                loginip: item.value,
                                port: '80',
                                username: 'admin',
                                password: this.password,
                                deviceIdentify: '',
                                channels: [],
                                channelId: ''
                            })
                            // this.devices[index].loginip = item.value
                        })
                        setTimeout(() => {
                            this.getWebVideoCtrl();
                        }, 500)
                    }
                }, {
                    immediate: true
                }
            )
        }
    },
    beforeDestroy() {
        // 移除视频插件内容
        const pluginDiv = document.getElementById('divPlugin');
        if (pluginDiv) {
            pluginDiv.innerHTML = '';
            pluginDiv.style.display = 'none';
        }
        this.stopPreview();
        this.logoutAll();
        this.hidPlugin();
    },
    methods: {
        showPlugin() {
            window.WebVideoCtrl.I_ShowPlugin().then(() => {
                this.showOPInfo('展示成功！')
            }).catch(() => {
                this.showOPInfo('展示失败！')
            })

        },
        hidPlugin() {
            window.WebVideoCtrl.I_HidPlugin().then(() => {
                this.showOPInfo('隐藏成功！')
            }).catch(() => {
                this.showOPInfo('隐藏失败！')
            })
        },
        getWebVideoCtrl() {
            // 初始化插件，设置为四分屏
            window.WebVideoCtrl.I_InitPlugin({
                bWndFull: true,
                iWndowType: this.wndCount, // 修改为4分屏
                cbSelWnd: (xmlDoc) => {
                    // 获取当前选择的窗口编号
                    this.wndIndex = parseInt(window.$(xmlDoc).find("SelectWnd").eq(0).text(), 10);
                    this.showOPInfo("当前选择的窗口编号：" + this.wndIndex);
                },
                cbInitPluginComplete: () => {
                    // 插件初始化成功后插入OBJECT插件
                    window.WebVideoCtrl.I_InsertOBJECTPlugin("divPlugin").then(() => {
                        this.showOPInfo("插件初始化成功！");
                        // 自动登录
                        this.loginAll();
                    }, () => {
                        this.showOPInfo("插件初始化失败，请确认是否已安装插件！");
                    });
                }
            });
        },
        // 显示操作信息
        showOPInfo(msg) {
            this.opinfo = `<div>${this.dateFormat(new Date(), "yyyy-MM-dd hh:mm:ss")} ${msg}</div>` + this.opinfo;
            console.log(`<>${this.dateFormat(new Date(), "yyyy-MM-dd hh:mm:ss")} ${msg}`);
        },
        // 日期格式化
        dateFormat(date, fmt) {
            const pad = (n) => n < 10 ? '0' + n : n;
            return fmt.replace('yyyy', date.getFullYear())
                .replace('MM', pad(date.getMonth() + 1))
                .replace('dd', pad(date.getDate()))
                .replace('hh', pad(date.getHours()))
                .replace('mm', pad(date.getMinutes()))
                .replace('ss', pad(date.getSeconds()));
        },
        // 登录并预览所有设备
        loginAll() {
            for (let i = 0; i < this.devices.length; i++) {
                this.loginDevice(i);
            }
        },
        // 退出所有设备
        logoutAll() {
            for (let i = 0; i < this.devices.length; i++) {
                this.logoutDevice(i);
            }
        },
        // 登录单个设备
        loginDevice(idx) {
            const dev = this.devices[idx];
            if (!dev.loginip || !dev.port) return;
            const deviceIdentify = `${dev.loginip}_${dev.port}`;
            window.WebVideoCtrl.I_Login(dev.loginip, 1, dev.port, dev.username, dev.password, {
                timeout: 3000,
                success: () => {
                    dev.deviceIdentify = deviceIdentify;
                    this.showOPInfo(`设备${idx+1} ${deviceIdentify} 登录成功！`);
                    this.getChannels(idx);
                },
                error: (err) => {
                    this.showOPInfo(`设备${idx+1} ${deviceIdentify} 登录失败！` + (err.errorMsg || ''));
                }
            });
        },
        // 退出单个设备
        logoutDevice(idx) {
            const dev = this.devices[idx];
            if (!dev.deviceIdentify) return;
            window.WebVideoCtrl.I_Logout(dev.deviceIdentify).then(() => {
                this.showOPInfo(`设备${idx+1} ${dev.deviceIdentify} 退出成功！`);
                dev.deviceIdentify = '';
                dev.channels = [];
                dev.channelId = '';
            }, () => {
                this.showOPInfo(`设备${idx+1} ${dev.deviceIdentify} 退出失败！`);
            });
        },
        // 获取通道信息并自动预览第一个通道
        getChannels(idx) {
            const dev = this.devices[idx];
            if (!dev.deviceIdentify) return;
            window.WebVideoCtrl.I_GetAnalogChannelInfo(dev.deviceIdentify, {
                success: (xmlDoc) => {
                    const arr = [];
                    window.$(xmlDoc).find("VideoInputChannel").each(function (i) {
                        const id = window.$(this).find("id").eq(0).text();
                        let name = window.$(this).find("name").eq(0).text();
                        if (!name) name = "Camera " + (i < 9 ? "0" + (i + 1) : (i + 1));
                        arr.push({
                            id,
                            name
                        });
                    });
                    dev.channels = arr;
                    dev.channelId = arr.length ? arr[0].id : '';
                    this.showOPInfo(`设备${idx+1} ${dev.deviceIdentify} 获取模拟通道成功！`);
                    // 自动预览第一个通道到对应窗口
                    if (arr.length) {
                        this.previewChannel(idx, arr[0].id);
                    }
                },
                error: (err) => {
                    this.showOPInfo(`设备${idx+1} ${dev.deviceIdentify} 获取模拟通道失败！` + (err.errorMsg || ''));
                }
            });
        },
        // 预览指定通道到指定窗口
        previewChannel(idx, channelId) {
            const dev = this.devices[idx];
            window.WebVideoCtrl.I_StartRealPlay(dev.deviceIdentify, {
                iStreamType: 1,
                iChannelID: parseInt(channelId, 10),
                iPort: 554,
                iWndIndex: idx,
                success: () => {
                    this.showOPInfo(`设备${idx+1} 通道${channelId} 预览成功！`);
                },
                error: (err) => {
                    this.showOPInfo(`设备${idx+1} 通道${channelId} 预览失败！` + (err.errorMsg || ''));
                }
            });
        },
        // 停止预览
        stopPreview() {
            // 停止所有窗口预览
            for (let i = 0; i < this.devices.length; i++) {
                window.WebVideoCtrl.I_Stop({
                    iWndIndex: i,
                    success: () => {
                        this.showOPInfo(`窗口${i+1} 停止预览成功！`);
                    },
                    error: (err) => {
                        this.showOPInfo(`窗口${i+1} 停止预览失败！` + (err.errorMsg || ''));
                    }
                });
            }
        },
    }
}
</script>

<style>

</style>
