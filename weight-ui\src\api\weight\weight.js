import request from '@/utils/request'

// 查询称重记录列表
export function listWeight(query) {
  return request({
    url: '/weight/weight/list',
    method: 'get',
    params: query
  })
}

// 查询称重记录详细
export function getWeight(weightId) {
  return request({
    url: '/weight/weight/' + weightId,
    method: 'get'
  })
}

// 新增称重记录
export function addWeight(data) {
  return request({
    url: '/weight/weight',
    method: 'post',
    data: data
  })
}

// 修改称重记录
export function updateWeight(data) {
  return request({
    url: '/weight/weight',
    method: 'put',
    data: data
  })
}

// 删除称重记录
export function delWeight(weightId) {
  return request({
    url: '/weight/weight/' + weightId,
    method: 'delete'
  })
}

//称重接口
export function weight(data) {
  return request({
    url: '/weight/weight/weigh',
    method: 'post',
    data: data
  })
}

//存皮接口
export function saveSkin(data) {
  return request({
    url: '/basicInfo/vehicle/addtare',
    method: 'post',
    data: data
  })
}

//车牌回调接口
export function licensePlateCallback(data) {
  return request({
    url: '/weight/weight/getLicensePlate',
    method: 'post',
    data: data,
  })
}

