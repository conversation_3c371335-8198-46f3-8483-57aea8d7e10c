import { getToken } from "@/utils/auth";

/**
 * 导入功能通用混入
 */
export default {
  methods: {
    // 创建导入配置
    createImportConfig(apiPath) {
      return {
        open: false,
        title: "",
        isUploading: false,
        updateSupport: 0,
        headers: {
          Authorization: "Bearer " + getToken()
        },
        url: process.env.VUE_APP_BASE_API + apiPath
      };
    },
    
    // 处理文件上传进度
    handleFileUploadProgress(uploadConfig) {
      return (event, file, fileList) => {
        uploadConfig.isUploading = true;
      };
    },
    
    // 处理文件上传成功
    handleFileUploadSuccess(uploadConfig, refName, successCallback) {
      return (response, file, fileList) => {
        uploadConfig.open = false;
        uploadConfig.isUploading = false;
        this.$refs[refName].clearFiles();
        this.$alert(
          "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + 
          response.msg + 
          "</div>", 
          "导入结果", 
          {
            dangerouslyUseHTMLString: true
          }
        );
        if (successCallback) {
          successCallback();
        }
      };
    },
    
    // 提交上传文件
    submitFile(refName) {
      this.$refs[refName].submit();
    },
    
    // 下载模板
    downloadTemplate(apiPath, params = {}, filename) {
      if (this.download) {
        this.download(apiPath, params, filename);
      }
    }
  }
};