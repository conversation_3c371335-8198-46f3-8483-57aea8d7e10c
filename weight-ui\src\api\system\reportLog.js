import request from '@/utils/request'

// 查询报文交互日志列表
export function listReportLog(query) {
  return request({
    url: '/system/reportLog/list',
    method: 'get',
    params: query
  })
}

// 查询报文交互日志详细
export function getReportLog(logId) {
  return request({
    url: '/system/reportLog/' + logId,
    method: 'get'
  })
}

// 新增报文交互日志
export function addReportLog(data) {
  return request({
    url: '/system/reportLog',
    method: 'post',
    data: data
  })
}

// 修改报文交互日志
export function updateReportLog(data) {
  return request({
    url: '/system/reportLog',
    method: 'put',
    data: data
  })
}

// 删除报文交互日志
export function delReportLog(logId) {
  return request({
    url: '/system/reportLog/' + logId,
    method: 'delete'
  })
}
