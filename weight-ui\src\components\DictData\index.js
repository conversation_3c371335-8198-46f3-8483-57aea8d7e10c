/**
 * 字典数据组件
 * 提供字典数据的获取和搜索功能
 */

import Vue from 'vue'
import store from '@/store'
import DataDict from '@/utils/dict'
import { getDicts as getDicts } from '@/api/system/dict/data'

/**
 * 根据key在字典中搜索对应的值
 * @param {Array} dict 字典数组
 * @param {String} key 要搜索的key
 * @returns {Object|null} 找到的字典值或null
 */
function searchDictByKey(dict, key) {
  // 检查key是否为空
  if (key == null && key == "") {
    return null
  }
  try {
    // 遍历字典数组查找匹配的key
    for (let i = 0; i < dict.length; i++) {
      if (dict[i].key == key) {
        return dict[i].value
      }
    }
  } catch (e) {
    return null
  }
}

/**
 * 安装字典插件
 * 配置字典元数据和请求方式
 */
function install() {
  Vue.use(DataDict, {
    metas: {
      // 通配符配置，适用于所有字典类型
      '*': {
        labelField: 'dictLabel',  // 标签字段名
        valueField: 'dictValue',  // 值字段名
        // 字典数据请求方法
        request(dictMeta) {
          // 先从store中查找字典数据
          const storeDict = searchDictByKey(store.getters.dict, dictMeta.type)
          if (storeDict) {
            // 如果store中有，直接返回
            return new Promise(resolve => { resolve(storeDict) })
          } else {
            // 如果store中没有，从接口获取
            return new Promise((resolve, reject) => {
              getDicts(dictMeta.type).then(res => {
                // 获取成功后存入store
                store.dispatch('dict/setDict', { key: dictMeta.type, value: res.data })
                resolve(res.data)
              }).catch(error => {
                reject(error)
              })
            })
          }
        },
      },
    },
  })
}

// 导出安装方法
export default {
  install,
}