const { contextBridge, ipc<PERSON>enderer } = require('electron')

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 获取应用版本
  getVersion: () => ipcRenderer.invoke('app-version'),
  
  // 显示消息框
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  
  // 退出应用
  quit: () => ipcRenderer.invoke('app-quit'),
  
  // 获取API基础URL - 通过主进程获取
  getApiBaseUrl: () => ipcRenderer.invoke('get-api-base-url'),
  
  // 平台信息
  platform: process.platform,
  
  // 监听渲染进程消息
  onMessage: (callback) => {
    ipcRenderer.on('message', callback)
  },
  
  // 移除消息监听
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel)
  }
})

// 窗口加载完成后的处理
window.addEventListener('DOMContentLoaded', () => {
  // 可以在这里添加一些初始化逻辑
  console.log('Electron preload script loaded')
  
  // 添加一个标识，表明这是在Electron环境中运行
  document.body.classList.add('electron-app')
  
  // 设置一些全局变量供Vue应用使用
  window.isElectron = true
  window.electronVersion = process.versions.electron
  window.nodeVersion = process.versions.node
  window.chromeVersion = process.versions.chrome
})