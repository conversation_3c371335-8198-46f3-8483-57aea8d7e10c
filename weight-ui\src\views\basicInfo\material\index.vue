<template>
  <div class="material-spec-layout">
    <div class="layout-container">
      <!-- 左侧物料区域 30% -->
      <div class="material-section">
        <MaterialList @material-change="handleMaterialChange" />
      </div>
      
      <!-- 右侧规格区域 70% -->
      <div class="spec-section">
        <SpecList :current-material="currentMaterial" @spec-change="handleSpecChange" />
      </div>
    </div>
  </div>
</template>

<script>
import MaterialList from "@/components/MaterialList";
import SpecList from "@/components/SpecList";

export default {
  name: "MaterialSpec",
  components: {
    MaterialList,
    SpecList
  },
  data() {
    return {
      currentMaterial: null,
      currentSpec: null
    };
  },
  methods: {
    // 处理物料变更事件
    handleMaterialChange(material) {
      this.currentMaterial = material;
      this.currentSpec = null;
    },
    
    // 处理规格变更事件
    handleSpecChange(spec) {
      this.currentSpec = spec;
    }
  }
};
</script>

<style scoped>
.material-spec-layout {
  height: calc(100vh - 50px);
  background: #f4f6fa;
  box-sizing: border-box;
  overflow: hidden;
}

.layout-container {
  display: flex;
  height: 100%;
  gap: 16px;
  padding: 16px;
}

.material-section {
  width: 50%;
  min-width: 320px;
  max-width: none;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.spec-section {
  flex: 1;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
/* 桌面端 (>= 1200px) - 默认 50%-50% */
@media (min-width: 1200px) {
  .material-section {
    width: 40%;
  }
}

/* 平板端 (768px - 1199px) - 50%-50% */
@media (max-width: 1199px) and (min-width: 768px) {
  .material-section {
    width: 40%;
    min-width: 300px;
  }
}

/* 移动端 (< 768px) - 垂直堆叠 */
@media (max-width: 767px) {
  .layout-container {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }
  
  .material-section,
  .spec-section {
    width: 100%;
    max-width: none;
    min-width: auto;
  }
  
  .material-section {
    flex: 0 0 auto;
    max-height: 40vh;
  }
  
  .spec-section {
    flex: 1;
    min-height: 300px;
  }
}
</style>
