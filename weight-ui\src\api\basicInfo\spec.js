import request from '@/utils/request'

// 查询物料规格列表
export function listSpec(query) {
  return request({
    url: '/basicInfo/spec/list',
    method: 'get',
    params: query
  })
}

// 查询物料规格详细
export function getSpec(specId) {
  return request({
    url: '/basicInfo/spec/' + specId,
    method: 'get'
  })
}

// 新增物料规格
export function addSpec(data) {
  return request({
    url: '/basicInfo/spec',
    method: 'post',
    data: data
  })
}

// 修改物料规格
export function updateSpec(data) {
  return request({
    url: '/basicInfo/spec',
    method: 'put',
    data: data
  })
}

// 删除物料规格
export function delSpec(specId) {
  return request({
    url: '/basicInfo/spec/' + specId,
    method: 'delete'
  })
}


