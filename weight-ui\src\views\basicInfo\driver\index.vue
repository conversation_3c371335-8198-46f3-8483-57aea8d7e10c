<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="司机名称" prop="driverName">
            <el-input v-model="queryParams.driverName" placeholder="请输入司机名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="司机编码" prop="driverCode">
            <el-input v-model="queryParams.driverCode" placeholder="请输入司机编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="部门名称" prop="deptId" v-if="false">
            <el-select v-model="queryParams.deptId" placeholder="请选择部门名称" clearable>
                <el-option v-for="dict in dict.type.sys_job_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="司机类型" prop="driverType">
            <el-select v-model="queryParams.driverType" placeholder="请选择司机类型" clearable>
                <el-option v-for="dict in dict.type.driver_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态">
                <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['basicInfo:driver:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['basicInfo:driver:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['basicInfo:driver:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport" v-hasPermi="['basicInfo:driver:import']">导入</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['basicInfo:driver:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="driverList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="司机名称" align="center" prop="driverName" />
        <el-table-column label="司机编码" align="center" prop="driverCode" />
        <el-table-column label="部门名称" align="center" prop="deptId" v-if="false">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.sys_job_status" :value="scope.row.deptId" />
            </template>
        </el-table-column>
        <el-table-column label="司机类型" align="center" prop="driverType">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.driver_type" :value="scope.row.driverType" />
            </template>
        </el-table-column>
        <el-table-column label="身份证" align="center" prop="identityCard" />
        <el-table-column label="驾照级别" align="center" prop="driverLevel" v-if="false">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.driver_type" :value="scope.row.driverLevel" />
            </template>
        </el-table-column>
        <el-table-column label="联系电话" align="center" prop="telephone" />
        <el-table-column label="信用分" align="center" prop="creditScore" v-if="false" />
        <el-table-column label="承运次数" align="center" prop="carryNum" v-if="false" />
        <el-table-column label="用户名称" align="center" prop="userId">
            <template slot-scope="scope">
                {{ userMap[scope.row.userId] || '' }}
            </template>
        </el-table-column>
        <el-table-column label="启用状态" align="center" prop="status">
            <template slot-scope="scope">
                <el-switch v-model="scope.row.status" active-value="Y" inactive-value="N" @change="handleStatusChange(scope.row)"></el-switch>
            </template>
        </el-table-column>
        <el-table-column label="取消接单次数" align="center" prop="spare1" v-if="false" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['basicInfo:driver:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['basicInfo:driver:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改司机对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="司机名称" prop="driverId" v-if="false">
                        <el-input v-model="form.driverId" placeholder="请输入司机名称" />
                    </el-form-item>
                    <el-form-item label="司机名称" prop="driverName">
                        <el-input v-model="form.driverName" placeholder="请输入司机名称" />
                    </el-form-item>
                    <el-form-item label="司机编码" prop="driverCode">
                        <el-input v-model="form.driverCode" placeholder="请输入司机编码" />
                    </el-form-item>
                    <el-form-item label="部门名称" prop="deptId" v-if="false">
                        <el-select v-model="form.deptId" placeholder="请选择部门名称" style="width:100%">
                            <el-option v-for="dict in dict.type.sys_job_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="司机类型" prop="driverType">
                        <el-select v-model="form.driverType" clearable placeholder="请选择司机类型" style="width:100%">
                            <el-option v-for="dict in dict.type.driver_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="启用状态" prop="status">
                        <el-select v-model="form.status" placeholder="请选择状态" style="width:100%">
                            <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="身份证" prop="identityCard">
                        <el-input v-model="form.identityCard" placeholder="请输入身份证" />
                    </el-form-item>
                    <el-form-item label="联系电话" prop="telephone">
                        <el-input v-model="form.telephone" placeholder="请输入联系电话" />
                    </el-form-item>
                    <el-form-item label="用户名称" prop="userId">
                        <el-select v-model="form.userId" placeholder="请选择用户名称" filterable clearable style="width:100%">
                            <el-option v-for="user in userList" :key="user.userId" :label="user.userName" :value="user.userId" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="驾照级别" prop="driverLevel" v-if="false">
                        <el-select v-model="form.driverLevel" placeholder="请选择驾照级别" style="width:100%">
                            <el-option v-for="dict in dict.type.driver_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="信用分" prop="creditScore" v-if="false">
                        <el-input v-model="form.creditScore" placeholder="请输入信用分" />
                    </el-form-item>
                    <el-form-item label="承运次数" prop="carryNum" v-if="false">
                        <el-input v-model="form.carryNum" placeholder="请输入承运次数" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>

    <!-- 司机导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
        <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip text-center" slot="tip">
                <div class="el-upload__tip" slot="tip">
                    <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的司机数据
                </div>
                <span>仅允许导入xls、xlsx格式文件。</span>
                <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
            </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listDriver,
    getDriver,
    delDriver,
    addDriver,
    updateDriver
} from "@/api/basicInfo/driver";
import {
    listUser,
} from "@/api/system/user";
import { getToken } from "@/utils/auth";

export default {
    name: "Driver",
    dicts: ['sys_job_status', 'driver_type', 'synchronization_status', 'sys_yes_no'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 司机表格数据
            driverList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                driverName: null,
                driverCode: null,
                deptId: null,
                driverType: null,
                status: 'Y',
            },
            userList: [],
            userMap: {}, // 新增：userId 到 userName 的映射
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                driverId: [{
                    required: true,
                    message: "司机名称不能为空",
                    trigger: "blur"
                }],
                driverName: [{
                    required: true,
                    message: "司机名称不能为空",
                    trigger: "blur"
                }],
                driverCode: [{
                    required: true,
                    message: "司机编码不能为空",
                    trigger: "blur"
                }],
                identityCard: [{
                        required: false,
                        message: "身份证不能为空",
                        trigger: "blur"
                    },
                    {
                        pattern: /^[1-9]\d{5}(18|19|20)?\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}([0-9Xx])$/,
                        message: "请输入正确的身份证号码",
                        trigger: "blur"
                    }
                ],
                telephone: [{
                        required: false,
                        message: "联系电话不能为空",
                        trigger: "blur"
                    },
                    {
                        pattern: /^((1[3-9]\d{9})|(\d{3,4}-\d{7,8}))$/,
                        message: "请输入正确的联系电话（手机号或座机号）",
                        trigger: "blur"
                    }
                ],
                userId: [{
                    required: false,
                    message: "请选择用户名称",
                    trigger: "change"
                }],
                status: [{
                    required: true,
                    message: "启用状态不能为空",
                    trigger: "change"
                }]
            },
            // 新增：submitForm按钮加载动画
            submitLoading: false,
            // 司机导入参数
            upload: {
                open: false,
                title: "",
                isUploading: false,
                updateSupport: 0,
                headers: {
                    Authorization: "Bearer " + getToken()
                },
                url: process.env.VUE_APP_BASE_API + "/basicInfo/driver/importData"
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询司机列表 */
        getList() {
            this.loading = true;
            listDriver(this.queryParams).then(response => {
                this.driverList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
            this.getUserList()
        },
        //用户查询
        getUserList() {
            listUser({
                pageNum: 1,
                pageSize: 999999,
            }).then(response => {
                this.userList = response.rows;
                // 新增：构建 userMap
                this.userMap = {};
                response.rows.forEach(user => {
                    this.userMap[user.userId] = user.userName;
                });
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                driverId: null,
                driverName: null,
                driverCode: null,
                deptId: null,
                driverType: null,
                identityCard: null,
                driverLevel: null,
                telephone: null,
                creditScore: null,
                carryNum: null,
                userId: null,
                spare1: null,
                spare2: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                status: 'Y',
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 司机状态修改
        handleStatusChange(row) {
            let text = row.status === "Y" ? "启用" : "停用";
            this.$modal.confirm('确认要"' + text + '""' + row.driverName + '"司机吗？').then(() => {
                return updateDriver(row);
            }).then(() => {
                this.$modal.msgSuccess(text + "成功");
            }).catch(() => {
                row.status = row.status === "Y" ? "N" : "Y";
            });
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.driverId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
       async handleAdd() {
            this.reset();
            const res = await this.$getAutoCode(); // 调用接口获取自动编码
            this.form.driverCode = res.msg; // 将自动编码赋值给表单
            this.open = true;
            this.title = "添加司机";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const driverId = row.driverId || this.ids
            getDriver(driverId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改司机";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.submitLoading = true;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.driverId != null) {
                        updateDriver(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    } else {
                        addDriver(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    }
                } else {
                    this.submitLoading = false;
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const driverIds = row.driverId || this.ids;
            this.$modal.confirm('是否确认删除司机编号为"' + driverIds + '"的数据项？').then(function () {
                return delDriver(driverIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('basicInfo/driver/export', {
                ...this.queryParams
            }, `driver_${new Date().getTime()}.xlsx`)
        },
        /** 导入按钮操作 */
        handleImport() {
            this.upload.title = "司机导入";
            this.upload.open = true;
        },
        /** 下载模板操作 */
        importTemplate() {
            this.download('basicInfo/driver/importTemplate', {}, `driver_template_${new Date().getTime()}.xlsx`)
        },
        // 文件上传中处理
        handleFileUploadProgress(event, file, fileList) {
            this.upload.isUploading = true;
        },
        // 文件上传成功处理
        handleFileSuccess(response, file, fileList) {
            this.upload.open = false;
            this.upload.isUploading = false;
            this.$refs.upload.clearFiles();
            this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {
                dangerouslyUseHTMLString: true
            });
            this.getList();
        },
        // 提交上传文件
        submitFileForm() {
            this.$refs.upload.submit();
        },
    }
};
</script>
