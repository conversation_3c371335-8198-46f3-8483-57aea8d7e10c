<template>
<div class="box_center" style="height: 100%">
    <div class="head">
        <h1><a>智能称重系统驾驶舱</a></h1>
        <div class="time" id="showTime">{{ time }}</div>
    </div>
    <div class="mainbox">
        <ul class="clearfix">
            <li>
                <div class="boxall" style="height: calc(58% - 0.15rem)">
                    <div class="alltitle">物料总发货数</div>
                    <div class="boxnav echarts4" id="echarts4" v-loading="isLoading('aWeekTotalList')">
                        <div v-if="hasError('aWeekTotalList')" class="error-state">
                            <p>{{ getErrorMessage('aWeekTotalList') }}</p>
                            <button @click="retryLoad('aWeekTotalList')" class="retry-btn">重试</button>
                        </div>
                    </div>
                </div>
                <div class="boxall" style="height: calc(42% - 0.15rem)">
                    <div class="alltitle">近七天物料出货统计</div>
                    <div class="boxnav echarts3" id="echarts3" v-loading="isLoading('trainNumber')">
                        <div v-if="hasError('trainNumber')" class="error-state">
                            <p>{{ getErrorMessage('trainNumber') }}</p>
                            <button @click="retryLoad('trainNumber')" class="retry-btn">重试</button>
                        </div>
                    </div>
                </div>
            </li>
            <li>
                <div class="boxall" style="height: calc(20% - 0.15rem)">
                    <ul class="row h100 clearfix">
                        <li class="col-6">
                            <div class="sqzs h100" v-loading="isLoading('todaySum')">
                                <p>今日出货量</p>
                                <h1 v-if="!hasError('todaySum')">
                                    <span>{{ todaySum }}</span>吨
                                </h1>
                                <div v-else class="error-state-inline">
                                    <p>{{ getErrorMessage('todaySum') }}</p>
                                    <button @click="retryLoad('todaySum')" class="retry-btn-small">重试</button>
                                </div>
                            </div>
                        </li>
                        <li class="col-6">
                            <div class="sqzs h100" v-loading="isLoading('totalSum')">
                                <p>总出货量</p>
                                <h1 v-if="!hasError('totalSum')">
                                    <span>{{ totalSum }}</span>吨
                                </h1>
                                <div v-else class="error-state-inline">
                                    <p>{{ getErrorMessage('totalSum') }}</p>
                                    <button @click="retryLoad('totalSum')" class="retry-btn-small">重试</button>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="boxall" style="height: calc(38% - 0.15rem)">
                    <div class="alltitle">年物料出货数</div>
                    <div class="boxnav echarts1" id="echarts1" v-loading="isLoading('aWeekYearList')">
                        <div v-if="hasError('aWeekYearList')" class="error-state">
                            <p>{{ getErrorMessage('aWeekYearList') }}</p>
                            <button @click="retryLoad('aWeekYearList')" class="retry-btn">重试</button>
                        </div>
                    </div>
                </div>
                <div class="boxall" style="height: calc(42% - 0.15rem)">
                    <div class="alltitle">月物料出货数</div>
                    <div class="boxnav echarts2" id="echarts2" v-loading="isLoading('aWeekList')">
                        <div v-if="hasError('aWeekList')" class="error-state">
                            <p>{{ getErrorMessage('aWeekList') }}</p>
                            <button @click="retryLoad('aWeekList')" class="retry-btn">重试</button>
                        </div>
                    </div>
                </div>
            </li>
            <li>
                <div class="boxall" style="height: calc(33.333% - 0.15rem)">
                    <div class="alltitle">客户物料发货数</div>
                    <div class="boxnav echarts5" id="echarts5" v-loading="isLoading('customerList')">
                        <div v-if="hasError('customerList')" class="error-state">
                            <p>{{ getErrorMessage('customerList') }}</p>
                            <button @click="retryLoad('customerList')" class="retry-btn">重试</button>
                        </div>
                    </div>
                </div>
                <div class="boxall" style="height: calc(33.333% - 0.15rem);overflow: auto;">
                    <div class="alltitle">物料出货数统计</div>
                    <div class="boxnav" v-loading="isLoading('reportList')">
                        <div v-if="hasError('reportList')" class="error-state">
                            <p>{{ getErrorMessage('reportList') }}</p>
                            <button @click="retryLoad('reportList')" class="retry-btn">重试</button>
                        </div>
                        <table v-else border="0" cellspacing="0">
                            <tbody>
                                <tr>
                                    <th>物料名称</th>
                                    <th>日发货数</th>
                                    <th>周发货数</th>
                                    <th>月发货数</th>
                                    <th>年发货数</th>
                                </tr>
                                <tr v-for="item in reportList" :key="item.id || item.specName">
                                    <th>{{ item.specName }}</th>
                                    <td>{{ item.daySum }}</td>
                                    <td>{{ item.aweekSum }}</td>
                                    <td>{{ item.monSum }}</td>
                                    <td>{{ item.yearSum }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="boxall" style="height: calc(33.333% - 0.15rem)">
                    <div class="alltitle">今日物料出货数</div>
                    <div class="boxnav echarts6" id="echarts6" style="height: calc(100% - 0.3rem)" v-loading="isLoading('todayList')">
                        <div v-if="hasError('todayList')" class="error-state">
                            <p>{{ getErrorMessage('todayList') }}</p>
                            <button @click="retryLoad('todayList')" class="retry-btn">重试</button>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
</template>

    
<script>
// import {
//     listReport,
//     todayList,
//     aWeekList,
//     aWeekTotalList,
//     todaySum,
//     totalSum,
//     trainNumber,
//     customerList,
//     aWeekYearList,
//     specForms
// } from "@/api/report/graphical";

// 静态常量抽离
const COLOR_ARR = [{
        colorsOne: '#8bd46e',
        colorsTwo: '#09bcb7'
    },
    {
        colorsOne: '#248ff7',
        colorsTwo: '#6851f1'
    },
    {
        colorsOne: '#fccb05',
        colorsTwo: '#f5804d'
    },
    {
        colorsOne: '#7CF7FF',
        colorsTwo: '#4B73FF'
    },
    {
        colorsOne: '#8A88FB',
        colorsTwo: '#D079EE'
    },
    {
        colorsOne: '#FFED46',
        colorsTwo: '#FF7EC7'
    },
    {
        colorsOne: '#8FFF85',
        colorsTwo: '#39A0FF'
    },
    {
        colorsOne: '#E0FF87',
        colorsTwo: '#8FB85B'
    },
    {
        colorsOne: '#FFDC99',
        colorsTwo: '#FF62C0'
    },
    {
        colorsOne: '#DDE4FF',
        colorsTwo: '#8DA2EE'
    },
];

// 图表管理器类
class ChartManager {
    constructor() {
        this.charts = new Map();
        this.resizeHandler = null;
    }

    // 创建或获取图表实例
    getChart(selector, echarts) {
        if (!this.charts.has(selector)) {
            const element = document.querySelector(selector);
            if (!element) {
                throw new Error(`图表容器未找到: ${selector}`);
            }
            const chart = echarts.init(element);
            this.charts.set(selector, chart);

            // 添加resize监听
            if (!this.resizeHandler) {
                this.resizeHandler = () => {
                    this.charts.forEach(chart => chart.resize());
                };
                window.addEventListener("resize", this.resizeHandler);
            }
        }
        return this.charts.get(selector);
    }

    // 安全设置图表配置
    setOption(selector, option, echarts) {
        try {
            const chart = this.getChart(selector, echarts);
            chart.setOption(option, true); // true表示不合并配置，提高性能
            return true;
        } catch (error) {
            console.error(`图表配置失败 ${selector}:`, error);
            return false;
        }
    }

    // 销毁所有图表
    dispose() {
        this.charts.forEach(chart => chart.dispose());
        this.charts.clear();
        if (this.resizeHandler) {
            window.removeEventListener("resize", this.resizeHandler);
            this.resizeHandler = null;
        }
    }
}

// 错误处理工具
const ErrorHandler = {
    // API错误处理
    handleApiError(error, context = '') {
        console.error(`API调用失败 ${context}:`, error);
        const message = error ?.response ?.data ?.message || error ?.message || '数据获取失败';
        return {
            success: false,
            message,
            data: null
        };
    },

    // 图表错误处理
    handleChartError(error, chartName = '') {
        console.error(`图表渲染失败 ${chartName}:`, error);
        return false;
    },

    // 通用错误处理
    handleError(error, context = '') {
        console.error(`操作失败 ${context}:`, error);
        return false;
    }
};

export default {
    name: "GraphicReport",
    data() {
        return {
            //遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 总条数
            total: 0,
            time: null,
            timer: null,
            //报表详情表格数据
            xAxisData: [],
            seriesList: [],
            // 报表详情表格数据
            reportList: [],
            todaySum: 0,
            totalSum: 0,
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                queryField: "0",
                queryCondition: "0",
                beginTime: "2021-1-1",
                endTime: "2100-12-31",
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
            chartTitle: "柱状图报表",
            chartDataName: "目录",
            pickerTime: [],
            colorArr: COLOR_ARR,
            queryFieldOptions: [],
            queryConditionOptions: [],
            option: {},
            optionBar: {},
            optionCenter: {},
            optionTopRight: {},
            optionLowerLeft: {},
            optionBottom: {},
            optionCategory: {},
            // 新增状态管理
            chartManager: null,
            loadingStates: {
                reportList: false,
                todayList: false,
                aWeekList: false,
                aWeekTotalList: false,
                todaySum: false,
                totalSum: false,
                trainNumber: false,
                customerList: false,
                aWeekYearList: false
            },
            errorStates: {
                reportList: null,
                todayList: null,
                aWeekList: null,
                aWeekTotalList: null,
                todaySum: null,
                totalSum: null,
                trainNumber: null,
                customerList: null,
                aWeekYearList: null
            }
        };
    },
    created() {
        // `this` 指向 vm 实例
        // this.getList();
    },
    watch: {
        "queryParams.queryField": function (val) {
            if (val) {
                this.fetchReportList();
            }
        },
        "queryParams.queryCondition": function (val) {
            if (val) {
                this.fetchReportList();
                if (this.queryParams.queryCondition === "3" && this.queryParams.queryField !== "3") {
                    this.msgWarning("请选择了物料再选择规格");
                    this.queryParams.queryCondition = "0";
                }
            }
        },
    },
    mounted() {
        this.setHtmlFontSize();
        window.addEventListener("resize", this.setHtmlFontSize);
        this.toggleFullScreen();

        // 初始化图表管理器
        this.chartManager = new ChartManager();

        // 初始化页面数据
        this.initPageData();

        this.getTime();
    },
    beforeDestroy() {
        // 清理定时器
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }

        // 移除事件监听
        window.removeEventListener("resize", this.setHtmlFontSize);

        // 销毁图表管理器
        if (this.chartManager) {
            this.chartManager.dispose();
            this.chartManager = null;
        }
    },
    methods: {
        // 初始化页面数据
        async initPageData() {
            this.loading = true;
            try {
                await this.loadAllData();
            } catch (error) {
                ErrorHandler.handleError(error, '初始化页面数据');
            } finally {
                this.loading = false;
            }
        },

        // 并行加载所有数据
        async loadAllData() {
            const tasks = [
                this.loadReportList(),
                this.loadTodayList(),
                this.loadWeekList(),
                this.loadWeekTotalList(),
                this.loadTodaySum(),
                this.loadTotalSum(),
                this.loadTrainNumber(),
                this.loadCustomerList(),
                this.loadYearList()
            ];

            await Promise.allSettled(tasks);
        },

        // 设置加载状态
        setLoadingState(key, loading) {
            this.$set(this.loadingStates, key, loading);
        },

        // 设置错误状态
        setErrorState(key, error) {
            this.$set(this.errorStates, key, error);
        },

        // 安全的API调用
        async safeApiCall(apiFunc, params, key, successCallback) {
            this.setLoadingState(key, true);
            this.setErrorState(key, null);

            try {
                const response = await apiFunc(params);
                if (successCallback) {
                    successCallback(response);
                }
                return response;
            } catch (error) {
                const errorResult = ErrorHandler.handleApiError(error, key);
                this.setErrorState(key, errorResult.message);
                throw error;
            } finally {
                this.setLoadingState(key, false);
            }
        },

        // 通用图表配置生成器
        createChartOption(type, config = {}) {
            const baseOption = {
                tooltip: {
                    trigger: config.tooltipTrigger || 'item',
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    textStyle: {
                        color: '#fff'
                    }
                },
                legend: {
                    textStyle: {
                        color: '#fff'
                    },
                    ...config.legend
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '10%',
                    containLabel: true,
                    ...config.grid
                }
            };

            switch (type) {
                case 'bar':
                    return this.createBarOption(baseOption, config);
                case 'line':
                    return this.createLineOption(baseOption, config);
                case 'pie':
                    return this.createPieOption(baseOption, config);
                case 'mixed':
                    return this.createMixedOption(baseOption, config);
                default:
                    return baseOption;
            }
        },

        // 柱状图配置
        createBarOption(baseOption, config) {
            return {
                ...baseOption,
                xAxis: {
                    type: 'category',
                    data: config.xAxisData || [],
                    axisLine: {
                        lineStyle: {
                            color: '#fff'
                        }
                    },
                    axisLabel: {
                        color: '#fff'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#fff'
                        }
                    },
                    axisLabel: {
                        color: '#fff'
                    }
                },
                series: [{
                    type: 'bar',
                    data: config.seriesData || [],
                    barWidth: config.barWidth || '60%',
                    itemStyle: {
                        borderRadius: config.borderRadius || 0,
                        color: config.color || this.colorArr[0].colorsOne
                    }
                }]
            };
        },

        // 折线图配置
        createLineOption(baseOption, config) {
            return {
                ...baseOption,
                xAxis: {
                    type: 'category',
                    data: config.xAxisData || [],
                    axisLine: {
                        lineStyle: {
                            color: '#fff'
                        }
                    },
                    axisLabel: {
                        color: '#fff'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: '#fff'
                        }
                    },
                    axisLabel: {
                        color: '#fff'
                    }
                },
                series: [{
                    type: 'line',
                    data: config.seriesData || [],
                    smooth: config.smooth || true,
                    lineStyle: {
                        color: config.color || this.colorArr[0].colorsOne,
                        width: config.lineWidth || 2
                    },
                    itemStyle: {
                        color: config.color || this.colorArr[0].colorsOne
                    }
                }]
            };
        },

        // 饼图配置
        createPieOption(baseOption, config) {
            return {
                ...baseOption,
                series: [{
                    type: 'pie',
                    data: config.seriesData || [],
                    radius: config.radius || ['40%', '70%'],
                    center: config.center || ['50%', '50%'],
                    itemStyle: {
                        borderRadius: config.borderRadius || 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: config.showLabel !== false,
                        color: '#fff'
                    }
                }]
            };
        },

        // 混合图配置
        createMixedOption(baseOption, config) {
            return {
                ...baseOption,
                xAxis: config.xAxis || [],
                yAxis: config.yAxis || [],
                series: config.series || []
            };
        },

        // 渲染图表的通用方法
        renderChart(selector, option, chartName = '') {
            if (!this.chartManager) {
                console.error('图表管理器未初始化');
                return false;
            }

            try {
                return this.chartManager.setOption(selector, option, this.$echarts);
            } catch (error) {
                return ErrorHandler.handleChartError(error, chartName);
            }
        },

        //屏幕自适应
        setHtmlFontSize() {
            const whei = window.innerWidth;
            document.querySelector("html").style.fontSize = whei / 20 + "px";
        },
        //实时时间
        getTime() {
            clearTimeout(this.timer);
            const dt = new Date();
            const y = dt.getFullYear();
            const mt = dt.getMonth() + 1;
            const day = dt.getDate();
            const h = dt.getHours(); //获取时
            const m = dt.getMinutes(); //获取分
            const s = dt.getSeconds(); //获取秒
            this.time =
                y +
                "/" +
                this.appendZero(mt) +
                "/" +
                this.appendZero(day) +
                " " +
                this.appendZero(h) +
                ":" +
                this.appendZero(m) +
                ":" +
                this.appendZero(s) +
                "";
            this.timer = setTimeout(this.getTime, 1000); //设定定时器，循环运行
        },
        appendZero(num) {
            return num < 10 ? "0" + num : num;
        },
        // 重构后的数据加载方法
        async loadReportList() {
            await this.safeApiCall(
                specForms, {
                    spare1: 1
                },
                'reportList',
                (res) => {
                    this.reportList = res.data;
                }
            );
        },

        async loadYearList() {
            await this.safeApiCall(
                aWeekYearList, {
                    spare1: 1
                },
                'aWeekYearList',
                (res) => {
                    this.processYearData(res.data);
                    this.renderChart('.echarts1', this.optionCenter, '年物料出货数');
                }
            );
        },

        async loadTodayList() {
            await this.safeApiCall(
                todayList, {
                    spare1: 1
                },
                'todayList',
                (res) => {
                    this.processTodayData(res.data);
                    this.renderChart('.echarts6', this.optionBar, '今日物料发货数');
                }
            );
        },

        async loadWeekList() {
            await this.safeApiCall(
                aWeekList, {
                    spare1: 1
                },
                'aWeekList',
                (res) => {
                    this.processWeekData(res.data);
                    this.renderChart('.echarts2', this.optionBottom, '月物料出货数');
                }
            );
        },

        async loadWeekTotalList() {
            await this.safeApiCall(
                aWeekTotalList, {
                    spare1: 1
                },
                'aWeekTotalList',
                (res) => {
                    this.processWeekTotalData(res.data);
                    this.renderChart('.echarts4', this.option, '物料总发货数');
                }
            );
        },

        async loadTodaySum() {
            await this.safeApiCall(
                todaySum, {
                    spare1: 1
                },
                'todaySum',
                (res) => {
                    this.todaySum = res.data;
                }
            );
        },

        async loadTotalSum() {
            await this.safeApiCall(
                totalSum, {
                    spare1: 1
                },
                'totalSum',
                (res) => {
                    this.totalSum = res.data;
                }
            );
        },

        async loadTrainNumber() {
            await this.safeApiCall(
                trainNumber, {
                    spare1: 1
                },
                'trainNumber',
                (res) => {
                    this.processTrainData(res.data);
                    this.renderChart('.echarts3', this.optionLowerLeft, '近七天出货统计');
                }
            );
        },

        async loadCustomerList() {
            await this.safeApiCall(
                customerList, {
                    spare1: 1
                },
                'customerList',
                (res) => {
                    this.processCustomerData(res.data);
                    this.renderChart('.echarts5', this.optionTopRight, '客户物料发货数');
                }
            );
        },

        // 数据处理方法
        processYearData(data) {
            this.optionCenter.legend = {
                data: []
            };
            this.optionCenter.series = [];

            data.forEach((item, index) => {
                this.optionCenter.legend.data.push(item.specName);
                this.optionCenter.xAxis = this.optionCenter.xAxis || [{}];
                this.optionCenter.xAxis[0].data = item.sumdata.days;

                // 柱状图
                this.optionCenter.series.push({
                    name: item.specName,
                    type: "bar",
                    data: item.sumdata.materialNames,
                    barWidth: "15%",
                    itemStyle: {
                        borderRadius: 15,
                        color: this.colorArr[index].colorsOne,
                    },
                    barGap: "0.2",
                });

                // 折线图
                this.optionCenter.series.push({
                    name: item.specName,
                    type: "line",
                    yAxisIndex: 1,
                    data: item.sumdata.incrementalRs,
                    lineStyle: {
                        width: 2,
                        color: this.colorArr[index].colorsOne,
                    },
                    itemStyle: {
                        color: this.colorArr[index].colorsOne,
                    },
                    smooth: true,
                });
            });
        },

        processTodayData(data) {
            let totalNum = 0;
            const processedData = data.map((item, index) => {
                totalNum += item.sunNetWeight;
                return {
                    value: item.sunNetWeight,
                    name: item.materialName,
                    itemStyle: {
                        color: this.colorArr[index].colorsTwo
                    }
                };
            });

            this.optionBar = this.createChartOption('pie', {
                seriesData: processedData,
                radius: ['40%', '70%']
            });

            // 添加中心文字
            this.optionBar.title = {
                text: totalNum.toFixed(2),
                left: 'center',
                top: 'middle',
                textStyle: {
                    color: '#fff',
                    fontSize: 24
                }
            };

            this.xAxisData = data;
        },

        processWeekData(data) {
            this.optionBottom.legend = {
                data: []
            };
            this.optionBottom.series = [];

            data.forEach((item, index) => {
                this.optionBottom.legend.data.push(item.specName);
                this.optionBottom.xAxis = this.optionBottom.xAxis || [{}];
                this.optionBottom.xAxis[0].data = item.sumdata.days;

                this.optionBottom.series.push({
                    name: item.specName,
                    type: "line",
                    smooth: true,
                    symbol: "circle",
                    symbolSize: 5,
                    showSymbol: false,
                    lineStyle: {
                        color: this.colorArr[index].colorsOne,
                        width: 2,
                    },
                    areaStyle: {
                        color: new this.$echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [{
                                    offset: 0,
                                    color: this.colorArr[index].colorsOne
                                },
                                {
                                    offset: 1,
                                    color: "rgba(228, 228, 126, 0)"
                                }
                            ],
                            false
                        ),
                        shadowColor: "rgba(0, 0, 0, 0.1)",
                    },
                    itemStyle: {
                        color: this.colorArr[index].colorsOne,
                        borderColor: "rgba(228, 228, 126, .1)",
                        borderWidth: 12,
                    },
                    data: item.sumdata.materialNames,
                });
            });
        },

        processWeekTotalData(data) {
            this.option.yAxis = this.option.yAxis || [{}, {}];
            this.option.series = this.option.series || [{}];

            this.option.yAxis[0].data = [];
            this.option.yAxis[1].data = [];
            this.option.series[0].data = [];

            const sortedData = data.specDatas.sort((a, b) => a.percentage - b.percentage);

            sortedData.forEach(element => {
                this.option.yAxis[0].data.push(element.name);
                this.option.yAxis[1].data.push(element.netweight);
                this.option.series[0].data.push(element.percentage);
            });
        },

        processTrainData(data) {
            this.optionLowerLeft.legend = {
                data: []
            };
            this.optionLowerLeft.series = [];

            data.forEach((item, index) => {
                this.optionLowerLeft.legend.data.push(item.specName);
                this.optionLowerLeft.xAxis = this.optionLowerLeft.xAxis || {};
                this.optionLowerLeft.xAxis.data = item.sumdata.days;

                this.optionLowerLeft.series.push({
                    name: item.specName,
                    type: "bar",
                    stack: "a",
                    barWidth: "30",
                    barGap: 0,
                    itemStyle: {
                        color: this.colorArr[Math.floor(index)].colorsOne,
                    },
                    data: item.sumdata.materialNames,
                });
            });
        },

        processCustomerData(data) {
            this.optionTopRight.xAxis = this.optionTopRight.xAxis || [{}];
            this.optionTopRight.series = this.optionTopRight.series || [{}];

            this.optionTopRight.xAxis[0].data = data.sumdata.customerNames;
            this.optionTopRight.series[0].data = data.sumdata.sunNetWeights;
        },
        randomHexColor() {
            //随机生成十六进制颜色
            var hex = Math.floor(Math.random() * 1546655684122).toString(16); //生成ffffff以内16进制数
            while (hex.length < 6) {
                //while循环判断hex位数，少于6位前面加0凑够6位
                hex = "0" + hex;
            }
            return "#" + hex; //返回'#'开头16进制颜色
        },
        //自动全屏
        toggleFullScreen() {
            const element = document.documentElement;
            if (!document.fullscreenElement) {
                element.requestFullscreen().catch((err) => {
                    console.log(
                        `Error attempting to enable full-screen mode: ${err.message} (${err.name})`
                    );
                });
            } else {
                document.exitFullscreen();
            }
        },
        /** 报表详情查询 */
        async fetchReportList() {
            this.loading = true;
            try {
                const [fieldRes, condRes, reportRes] = await Promise.all([
                    this.getDicts("query_field"),
                    this.getDicts("query_condition"),
                    this.listReport(this.queryParams),
                ]);
                this.queryFieldOptions = fieldRes.data;
                this.queryConditionOptions = condRes.data;
                this.xAxisData = reportRes.xAxisData;
                this.seriesList = reportRes.seriesList;
            } catch (e) {
                // 错误处理
                this.$message.error("获取报表数据失败");
            } finally {
                this.loading = false;
            }
        },
        // 表单重置
        resetFormFields() {
            this.form = {
                queryField: undefined,
                queryCondition: undefined,
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            //时间选择
            if (this.pickerTime[0] != null && this.pickerTime[1] != null) {
                this.queryParams.beginTime = this.pickerTime[0];
                this.queryParams.endTime = this.pickerTime[1];
            }
            this.fetchReportList();
        },
        setPx() {
            var docEl = document.documentElement;
            var dpr = window.devicePixelRatio || 1;

            // adjust body font size
            function setBodyFontSize() {
                if (document.body) {
                    document.body.style.fontSize = 12 * dpr + "px";
                } else {
                    document.addEventListener("DOMContentLoaded", setBodyFontSize);
                }
            }
            setBodyFontSize();

            // set 1rem = viewWidth / 10
            function setRemUnit() {
                var rem = docEl.clientWidth / 24;
                docEl.style.fontSize = rem + "px";
            }

            setRemUnit();

            // reset rem unit on page resize
            window.addEventListener("resize", setRemUnit);
            window.addEventListener("pageshow", function (e) {
                if (e.persisted) {
                    setRemUnit();
                }
            });

            // detect 0.5px supports
            if (dpr >= 2) {
                var fakeBody = document.createElement("body");
                var testElement = document.createElement("div");
                testElement.style.border = ".5px solid transparent";
                fakeBody.appendChild(testElement);
                docEl.appendChild(fakeBody);
                if (testElement.offsetHeight === 1) {
                    docEl.classList.add("hairlines");
                }
                docEl.removeChild(fakeBody);
            }
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
            this.pickerTime = [];
        },
        // 保留原有方法名以保持兼容性，但使用新的渲染方法
        showChart1() {
            this.renderChart('.echarts6', this.optionBar, '今日物料出货数');
        },
        showChart2() {
            this.renderChart('.echarts1', this.optionCenter, '年物料出货数');
        },
        showChart3() {
            this.renderChart('.echarts5', this.optionTopRight, '客户物料发货数');
        },
        showChart4() {
            this.renderChart('.echarts4', this.option, '物料总发货数');
        },
        showChart5() {
            this.renderChart('.echarts3', this.optionLowerLeft, '近七天出货统计');
        },
        showChart6() {
            this.renderChart('.echarts2', this.optionBottom, '月物料出货数');
        },

        // 检查是否有错误状态
        hasError(key) {
            return this.errorStates[key] !== null;
        },

        // 检查是否正在加载
        isLoading(key) {
            return this.loadingStates[key];
        },

        // 获取错误信息
        getErrorMessage(key) {
            return this.errorStates[key] || '加载失败';
        },

        // 重试加载数据
        async retryLoad(key) {
            const methodMap = {
                'reportList': this.loadReportList,
                'todayList': this.loadTodayList,
                'aWeekList': this.loadWeekList,
                'aWeekTotalList': this.loadWeekTotalList,
                'todaySum': this.loadTodaySum,
                'totalSum': this.loadTotalSum,
                'trainNumber': this.loadTrainNumber,
                'customerList': this.loadCustomerList,
                'aWeekYearList': this.loadYearList
            };

            const method = methodMap[key];
            if (method) {
                await method.call(this);
            }
        },
    },
};
</script>

    
<style scoped>
@charset "utf-8";

/* CSS Document */
* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

*,
.box_center {
    padding: 0px;
    margin: 0px;
    /* height: 100%; */
    /* height: 1080px; */
    font-family: "微软雅黑";
}

.box_center {
    background: #000d4a url(../../../assets/images/bg.jpg) center center;
    background-size: cover;
    color: #fff;
    font-size: 0.1rem;
}

li {
    list-style-type: none;
}

@font-face {
    font-family: electronicFont;
    src: url(../../../assets/font/DS-DIGIT.TTF);
}

i {
    margin: 0px;
    padding: 0px;
    text-indent: 0px;
}

img {
    border: none;
    max-width: 100%;
}

a {
    text-decoration: none;
    color: #399bff;
}

a.active,
a:focus {
    outline: none !important;
    text-decoration: none;
}

ol,
ul,
p,
h1,
h2,
h3,
h4,
h5,
h6 {
    padding: 0;
    margin: 0;
}

a:hover {
    color: #06c;
    text-decoration: none !important;
}

html,
body {
    height: 100%;
}

.clearfix:after,
.clearfix:before {
    display: table;
    content: " ";
}

.clearfix:after {
    clear: both;
}

.pulll_left {
    float: left;
}

.pulll_right {
    float: right;
}

/*谷哥滚动条样式*/
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    position: absolute;
}

::-webkit-scrollbar-thumb {
    background-color: #5bc0de;
}

::-webkit-scrollbar-track {
    background-color: #ddd;
}

.head {
    height: 1.05rem;
    background: url(../../../assets/images/head_bg.png) no-repeat center center;
    background-size: 100% 100%;
    position: relative;
}

.head h1 {
    color: #fff;
    text-align: center;
    font-size: 0.4rem;
    line-height: 0.8rem;
    letter-spacing: -1px;
}

.head h1 img {
    width: 1.5rem;
    display: inline-block;
    vertical-align: middle;
}

.time {
    position: absolute;
    right: 0.15rem;
    top: 0;
    line-height: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.3rem;
    padding-right: 0.1rem;
    font-family: electronicFont;
}

.mainbox {
    padding: 0 0.2rem 0rem 0.2rem;
    height: calc(100% - 1.05rem);
}

.mainbox>ul {
    margin-left: -0.1rem;
    margin-right: -0.1rem;
    height: 100%;
}

.mainbox>ul>li {
    float: left;
    padding: 0 0.1rem;
    height: 100%;
    width: 30%;
}

.mainbox>ul>li:nth-child(2) {
    width: 40%;
}

.boxall {
    padding: 0 0.2rem 0.2rem 0.2rem;
    background: rgba(6, 48, 109, 0.5);
    position: relative;
    margin-bottom: 0.15rem;
    z-index: 10;
}

.alltitle {
    font-size: 0.2rem;
    color: #fff;
    line-height: 0.5rem;
    position: relative;
    padding-left: 0.15rem;
}

.alltitle:before {
    position: absolute;
    height: 0.2rem;
    width: 4px;
    background: #49bcf7;
    border-radius: 5px;
    content: "";
    left: 0;
    top: 50%;
    margin-top: -0.1rem;
}

.boxnav {
    height: calc(100% - 0.5rem);
}

.row>li {
    float: left;
    height: 100%;
}

.col-6 {
    width: 50%;
}

.col-3 {
    width: 25%;
}

.col-4 {
    width: 33.33333%;
}

.h100 {
    height: 100%;
}

.tit01 {
    text-align: center;
    color: white;
    font-size: 0.16rem;
    height: fit-content;
    padding: 0.3rem 0 0.02rem 0;
}

.piebox {
    height: calc(100% - 0.5rem);
    position: relative;
}

.piebox:before {
    width: 0.6rem;
    height: 0.6rem;
    content: "";
    border: 1px solid #49bcf7;
    border-radius: 1rem;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -0.31rem;
    margin-top: -0.31rem;
    opacity: 0.7;
}

.sqzs {
    margin-right: 0.2rem;
}

.sqzs p {
    padding: 0.2rem 0 0.1rem 0;
    font-size: 0.22rem;
    display: block;
    /* height: fit-content; */
}

.sqzs h1 {
    height: calc(100% - 0.65rem);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    color: #fef000;
    font-weight: normal;
    letter-spacing: 2px;
    font-size: 0.5rem;
    justify-content: center;
    padding-bottom: 0.05rem;
}

.sqzs h1 span {
    font-size: 0.6rem;
    font-family: Impact, Haettenschweiler, "Arial Narrow Bold", sans-serif;
}

table {
    width: 100%;
    text-align: center;
}

table th {
    font-size: 0.16rem;
    background: rgba(0, 0, 0, 0.1);
}

table td {
    font-size: 0.16rem;
    color: rgba(255, 255, 255, 0.6);
}

table th,
table td {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0.1rem 0;
}

/* 错误状态样式 */
.error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #ff6b6b;
    text-align: center;
}

.error-state p {
    margin-bottom: 0.2rem;
    font-size: 0.14rem;
}

.error-state-inline {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #ff6b6b;
    text-align: center;
}

.error-state-inline p {
    margin-bottom: 0.1rem;
    font-size: 0.12rem;
}

.retry-btn {
    background: linear-gradient(45deg, #49bcf7, #6851f1);
    border: none;
    color: white;
    padding: 0.08rem 0.2rem;
    border-radius: 0.04rem;
    cursor: pointer;
    font-size: 0.12rem;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: linear-gradient(45deg, #6851f1, #49bcf7);
    transform: translateY(-1px);
}

.retry-btn-small {
    background: linear-gradient(45deg, #49bcf7, #6851f1);
    border: none;
    color: white;
    padding: 0.04rem 0.1rem;
    border-radius: 0.02rem;
    cursor: pointer;
    font-size: 0.1rem;
    transition: all 0.3s ease;
}

.retry-btn-small:hover {
    background: linear-gradient(45deg, #6851f1, #49bcf7);
    transform: translateY(-1px);
}

/* 加载状态优化 */
.el-loading-mask {
    background-color: rgba(0, 13, 74, 0.8) !important;
}

.el-loading-spinner .circular {
    stroke: #49bcf7 !important;
}

.el-loading-text {
    color: #49bcf7 !important;
}
</style>
