<template>
<div class="external-page-container">
    <!-- 使用iframe标签加载外部页面 -->
    <iframe :src="externalUrl" frameborder="0" width="100%" :title="iframeTitle"></iframe>
</div>
</template>

<script>
export default {
    name: 'ExternalPageEmbed',
    props: {
        // 外部页面的URL（必填）
        externalUrl: {
            type: String,
            required: true
        },
        // iframe的标题（可选）
        iframeTitle: {
            type: String,
            default: '嵌入的外部页面'
        }
    }
}
</script>

<style lang="scss" scoped>
.external-page-container {
    width: 100%;
    height: 100%;
    overflow: auto;

    /* 内容超出时显示滚动条 */
    iframe {
        height: calc(100vh - 50px);
    }
}
</style>
