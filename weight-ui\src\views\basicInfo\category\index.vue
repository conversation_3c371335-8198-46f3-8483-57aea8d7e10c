<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
        <el-form-item label="物料类别编码" prop="categoryCode">
            <el-input v-model="queryParams.categoryCode" placeholder="请输入物料类别编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="物料类别名称" prop="categoryName">
            <el-input v-model="queryParams.categoryName" placeholder="请输入物料类别名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['basicInfo:category:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['basicInfo:category:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['basicInfo:category:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['basicInfo:category:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="categoryList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="物料类别ID" align="center" prop="categoryId" v-if="false" />
        <el-table-column label="物料类别编码" align="center" prop="categoryCode" />
        <el-table-column label="物料类别名称" align="center" prop="categoryName" />
        <el-table-column label="部门名称" align="center" prop="deptId" v-if="false"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['basicInfo:category:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['basicInfo:category:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改物料类别对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body class="category-dialog">
        <el-form ref="form" :model="form" :rules="rules" label-width="115px" label-position="left">
            <el-form-item label="物料类别编码" prop="categoryCode" class="form-item" v-if="false">
                <el-input v-model="form.categoryCode" placeholder="请输入物料类别编码" clearable />
            </el-form-item>
            <el-form-item label="物料类别名称" prop="categoryName" class="form-item">
                <el-input v-model="form.categoryName" placeholder="请输入物料类别名称" clearable />
            </el-form-item>
            <el-form-item label="备用1" prop="spare1" v-if="false" class="hidden-item">
                <el-input v-model="form.spare1" placeholder="请输入备用1" />
            </el-form-item>
            <el-form-item label="备用2" prop="spare2" v-if="false" class="hidden-item">
                <el-input v-model="form.spare2" placeholder="请输入备用2" />
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="cancel" size="medium">取 消</el-button>
            <el-button type="primary" @click="submitForm" size="medium">
                确 定
            </el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listCategory,
    getCategory,
    delCategory,
    addCategory,
    updateCategory
} from "@/api/basicInfo/category";

export default {
    name: "Category",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 物料类别表格数据
            categoryList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                categoryCode: null,
                categoryName: null,
                deptId: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                categoryCode: [{
                    required: true,
                    message: "物料类别编码不能为空",
                    trigger: "blur"
                }],
                categoryName: [{
                    required: true,
                    message: "物料类别名称不能为空",
                    trigger: "blur"
                }],
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询物料类别列表 */
        getList() {
            this.loading = true;
            listCategory(this.queryParams).then(response => {
                this.categoryList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                categoryId: null,
                categoryCode: null,
                categoryName: null,
                deptId: null,
                spare1: null,
                spare2: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.categoryId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "添加物料类别";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const categoryId = row.categoryId || this.ids
            getCategory(categoryId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改物料类别";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.categoryId != null) {
                        updateCategory(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        });
                    } else {
                        addCategory(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        });
                    }
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const categoryIds = row.categoryId || this.ids;
            this.$modal.confirm('是否确认删除物料类别编号为"' + categoryIds + '"的数据项？').then(function () {
                return delCategory(categoryIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('basicInfo/category/export', {
                ...this.queryParams
            }, `category_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
