<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="车辆编号" prop="vehicleCode">
            <el-input v-model="queryParams.vehicleCode" placeholder="请输入车辆编号" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="车牌号码" prop="licensePlate">
            <el-input v-model="queryParams.licensePlate" placeholder="请输入车牌号码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="车辆类型" prop="vehicleType">
            <el-select v-model="queryParams.vehicleType" placeholder="请选择车辆类型" clearable>
                <el-option v-for="dict in dict.type.vehicle_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态">
                <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['basicInfo:vehicle:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['basicInfo:vehicle:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['basicInfo:vehicle:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport" v-hasPermi="['basicInfo:vehicle:import']">导入</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['basicInfo:vehicle:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="vehicleList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="车辆ID" align="center" prop="vehicleId" v-if="false" />
        <el-table-column label="车辆编号" align="center" prop="vehicleCode" />
        <el-table-column label="车牌号码" align="center" prop="licensePlate" />
        <el-table-column label="部门名称" align="center" prop="deptId" v-if="false" />
        <el-table-column label="车辆类型" align="center" prop="vehicleType">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.vehicle_type" :value="scope.row.vehicleType" />
            </template>
        </el-table-column>
        <el-table-column label="轮轴数量" align="center" prop="axesNum" />
        <el-table-column label="载重容量" align="center" prop="capacity" />
        <el-table-column label="可载物料类别" align="center" prop="categorys" v-if="false" />
        <el-table-column label="司机名称" align="center" prop="driverName" />
        <el-table-column label="车队名称" align="center" prop="fleetName" />
        <el-table-column label="物料名称" align="center" prop="materialId" v-if="false" />
        <el-table-column label="预置皮重" align="center" prop="presetTare" />
        <el-table-column label="预置皮重所需称重次数" align="center" prop="tareNum" />
        <el-table-column label="可载方量" align="center" prop="quantity" />
        <el-table-column label="皮重预警值" align="center" prop="tareWarning" />
        <el-table-column label="预置皮重的有效时间" align="center" prop="effectiveTime" width="180" v-if="false">
            <template slot-scope="scope">
                <span>{{ parseTime(scope.row.effectiveTime, '{y}-{m}-{d}') }}</span>
            </template>
        </el-table-column>
        <el-table-column label="启用状态" align="center" prop="status">
            <template slot-scope="scope">
                <el-switch v-model="scope.row.status" active-value="Y" inactive-value="N" @change="handleStatusChange(scope.row)"></el-switch>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['basicInfo:vehicle:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['basicInfo:vehicle:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改车辆对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="910px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="155px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="车辆编号" prop="vehicleCode">
                        <el-input v-model="form.vehicleCode" placeholder="请输入车辆编号" />
                    </el-form-item>
                    <el-form-item label="车牌号码" prop="licensePlate">
                        <el-input v-model="form.licensePlate" placeholder="请输入车牌号码" />
                    </el-form-item>
                    <el-form-item label="车辆类型" prop="vehicleType">
                        <el-select v-model="form.vehicleType" clearable placeholder="请选择车辆类型" style="width:100%">
                            <el-option v-for="dict in dict.type.vehicle_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="司机名称" prop="driverId">
                        <el-select v-model="form.driverId" filterable clearable placeholder="请选择司机名称" style="width:100%">
                            <el-option v-for="dict in driverList" :key="dict.driverId" :label="dict.driverName" :value="dict.driverId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="轮轴数量" prop="axesNum">
                        <el-input v-model="form.axesNum" placeholder="请输入轮轴数量" />
                    </el-form-item>
                    <el-form-item label="可载物料类别" prop="categorys" v-if="false">
                        <el-input v-model="form.categorys" placeholder="请输入可载物料类别" />
                    </el-form-item>
                    <el-form-item label="启用状态" prop="status">
                        <el-select v-model="form.status" placeholder="请选择状态" style="width:100%">
                            <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="预置皮重" prop="presetTare">
                        <el-input v-model="form.presetTare" placeholder="请输入预置皮重" />
                    </el-form-item>
                    <el-form-item label="预置皮重所需称重次数" prop="tareNum">
                        <el-input v-model="form.tareNum" placeholder="请输入预置皮重所需称重次数" />
                    </el-form-item>
                    <el-form-item label="可载方量" prop="quantity">
                        <el-input v-model="form.quantity" placeholder="请输入可载方量" />
                    </el-form-item>
                    <el-form-item label="车队名称" prop="fleetId">
                        <el-select v-model="form.fleetId" filterable clearable placeholder="请选择车队名称" style="width:100%">
                            <el-option v-for="dict in fleetList" :key="dict.fleetId" :label="dict.fleetName" :value="dict.fleetId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="皮重预警值" prop="tareWarning">
                        <el-input v-model="form.tareWarning" placeholder="请输入皮重预警值" />
                    </el-form-item>
                    <el-form-item label="载重容量" prop="capacity">
                        <el-input v-model="form.capacity" placeholder="请输入载重容量" />
                    </el-form-item>
                    <el-form-item label="预置皮重的有效时间" prop="effectiveTime" v-if="false">
                        <el-date-picker clearable v-model="form.effectiveTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择预置皮重的有效时间" style="width:100%">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="备用1" prop="spare1" v-if="false">
                        <el-input v-model="form.spare1" placeholder="请输入备用1" />
                    </el-form-item>
                    <el-form-item label="备用2" prop="spare2" v-if="false">
                        <el-input v-model="form.spare2" placeholder="请输入备用2" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>

    <!-- 车辆导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
        <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip text-center" slot="tip">
                <div class="el-upload__tip" slot="tip">
                    <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的车辆数据
                </div>
                <span>仅允许导入xls、xlsx格式文件。</span>
                <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
            </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listVehicle,
    getVehicle,
    delVehicle,
    addVehicle,
    updateVehicle
} from "@/api/basicInfo/vehicle";
import {
    listDriver,
} from "@/api/basicInfo/driver";
import {
    listFleet,
} from "@/api/basicInfo/fleet";
import { getToken } from "@/utils/auth";

export default {
    name: "Vehicle",
    dicts: ['vehicle_type', 'sys_yes_no'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            driverList: [], //司机列表
            fleetList: [], //车队列表
            // 车辆表格数据
            vehicleList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                vehicleCode: null,
                licensePlate: null,
                deptId: null,
                vehicleType: null,
                driverId: null,
                fleetId: null,
                materialId: null,
                status:'Y',
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                vehicleCode: [{
                    required: true,
                    message: "车辆编号不能为空",
                    trigger: "blur"
                }],
                licensePlate: [{
                    required: true,
                    message: "车牌号码不能为空",
                    trigger: "blur"
                }],
                telephone: [
                    { required: false, message: "联系电话不能为空", trigger: "blur" },
                    { 
                        pattern: /^((1[3-9]\d{9})|(\d{3,4}-\d{7,8}))$/, 
                        message: "请输入正确的联系电话（手机号或座机号）", 
                        trigger: "blur"
                    }
                ],
                status: [
                    { required: true, message: "启用状态不能为空", trigger: "change" }
                ]
            },
            // 新增：提交按钮loading
            submitLoading: false,
            // 车辆导入参数
            upload: {
                open: false,
                title: "",
                isUploading: false,
                updateSupport: 0,
                headers: {
                    Authorization: "Bearer " + getToken()
                },
                url: process.env.VUE_APP_BASE_API + "/basicInfo/vehicle/importData"
            },
        };
    },
    created() {
        this.getList();
        this.init();
    },
    methods: {
        /** 查询车辆列表 */
        getList() {
            this.loading = true;
            listVehicle(this.queryParams).then(response => {
                this.vehicleList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        //数据初始化
        init() {
            listDriver({
                pageNum: 1,
                pageSize: 999999
            }).then(response => {
                this.driverList = response.rows;
            });
            listFleet({
                pageNum: 1,
                pageSize: 999999
            }).then(response => {
                this.fleetList = response.rows;
            })
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                vehicleId: null,
                vehicleCode: null,
                licensePlate: null,
                deptId: null,
                vehicleType: null,
                axesNum: null,
                capacity: null,
                categorys: null,
                driverId: null,
                fleetId: null,
                materialId: null,
                presetTare: null,
                tareNum: null,
                quantity: null,
                tareWarning: null,
                effectiveTime: null,
                spare1: null,
                spare2: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                status: 'Y',
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 车辆状态修改
        handleStatusChange(row) {
            let text = row.status === "Y" ? "启用" : "停用";
            this.$modal.confirm('确认要"' + text + '""' + row.vehicleName + '"车辆吗？').then(() => {
                return updateVehicle(row);
            }).then(() => {
                this.$modal.msgSuccess(text + "成功");
            }).catch(() => {
                row.status = row.status === "Y" ? "N" : "Y";
            });
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.vehicleId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        async handleAdd() {
            this.reset();
            const res = await this.$getAutoCode()
            this.form.vehicleCode = res.msg
            this.open = true;
            this.title = "添加车辆";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const vehicleId = row.vehicleId || this.ids
            getVehicle(vehicleId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改车辆";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.submitLoading = true;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.vehicleId != null) {
                        updateVehicle(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    } else {
                        addVehicle(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    }
                } else {
                    this.submitLoading = false;
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const vehicleIds = row.vehicleId || this.ids;
            this.$modal.confirm('是否确认删除车辆编号为"' + vehicleIds + '"的数据项？').then(function () {
                return delVehicle(vehicleIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导入按钮操作 */
        handleImport() {
            this.upload.title = "车辆导入";
            this.upload.open = true;
        },
        /** 下载模板操作 */
        importTemplate() {
            this.download('basicInfo/vehicle/importTemplate', {}, `vehicle_template_${new Date().getTime()}.xlsx`)
        },
        // 文件上传中处理
        handleFileUploadProgress(event, file, fileList) {
            this.upload.isUploading = true;
        },
        // 文件上传成功处理
        handleFileSuccess(response, file, fileList) {
            this.upload.open = false;
            this.upload.isUploading = false;
            this.$refs.upload.clearFiles();
            this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {
                dangerouslyUseHTMLString: true
            });
            this.getList();
        },
        // 提交上传文件
        submitFileForm() {
            this.$refs.upload.submit();
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('basicInfo/vehicle/export', {
                ...this.queryParams
            }, `vehicle_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>
