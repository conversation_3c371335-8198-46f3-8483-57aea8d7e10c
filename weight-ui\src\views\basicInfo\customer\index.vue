<template>
<div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="客户名称" prop="customerName">
            <el-input v-model="queryParams.customerName" placeholder="请输入客户名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="客户编码" prop="customerCode">
            <el-input v-model="queryParams.customerCode" placeholder="请输入客户编码" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="部门名称" prop="deptId" v-if="false">
            <el-select v-model="queryParams.deptId" placeholder="请选择部门名称" clearable>
                <el-option v-for="dict in dict.type.sys_job_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="客户级别" prop="customerLevel">
            <el-select v-model="queryParams.customerLevel" placeholder="请选择客户级别" clearable>
                <el-option v-for="dict in dict.type.supplier_level" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="客户类型" prop="customerType">
            <el-select v-model="queryParams.customerType" placeholder="请选择客户类型" clearable>
                <el-option v-for="dict in dict.type.client_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item label="启用状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" >
                <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['basicInfo:customer:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" v-hasPermi="['basicInfo:customer:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['basicInfo:customer:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport" v-hasPermi="['basicInfo:customer:import']">导入</el-button>
        </el-col>
        <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['basicInfo:customer:export']">导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="customerList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="客户名称" align="center" prop="customerName" />
        <el-table-column label="客户编码" align="center" prop="customerCode" />
        <el-table-column label="部门名称" align="center" prop="deptId" v-if="false">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.sys_job_status" :value="scope.row.deptId" />
            </template>
        </el-table-column>
        <el-table-column label="客户级别" align="center" prop="customerLevel">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.sys_oper_type" :value="scope.row.customerLevel" />
            </template>
        </el-table-column>
        <el-table-column label="客户类型" align="center" prop="customerType">
            <template slot-scope="scope">
                <dict-tag :options="dict.type.client_type" :value="scope.row.customerType" />
            </template>
        </el-table-column>
        <el-table-column label="联系人" align="center" prop="linkMan" />
        <el-table-column label="联系电话" align="center" prop="telephone" />
        <el-table-column label="联系地址" align="center" prop="address" />
        <el-table-column label="邮箱" align="center" prop="email" />
        <el-table-column label="启用状态" align="center" prop="status">
            <template slot-scope="scope">
                <el-switch v-model="scope.row.status" active-value="Y" inactive-value="N" @change="handleStatusChange(scope.row)"></el-switch>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['basicInfo:customer:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['basicInfo:customer:remove']">删除</el-button>
            </template>
        </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改客户对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="客户名称" prop="customerName">
                        <el-input v-model="form.customerName" placeholder="请输入客户名称" />
                    </el-form-item>
                    <el-form-item label="客户编码" prop="customerCode">
                        <el-input v-model="form.customerCode" placeholder="请输入客户编码" />
                    </el-form-item>
                    <el-form-item label="部门名称" prop="deptId" v-if="false">
                        <el-select v-model="form.deptId" filterable clearable placeholder="请选择部门名称">
                            <el-option v-for="dict in dict.type.sys_job_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="客户级别" prop="customerLevel">
                        <el-select v-model="form.customerLevel" clearable placeholder="请选择客户级别">
                            <el-option v-for="dict in dict.type.supplier_level" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="form.email" placeholder="请输入邮箱" />
                    </el-form-item>
                    <el-form-item label="启用状态" prop="status">
                        <el-select v-model="form.status" placeholder="请选择状态" style="width:100%">
                            <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="客户类型" prop="customerType">
                        <el-select v-model="form.customerType" clearable placeholder="请选择客户类型">
                            <el-option v-for="dict in dict.type.client_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="联系人" prop="linkMan">
                        <el-input v-model="form.linkMan" placeholder="请输入联系人" />
                    </el-form-item>
                    <el-form-item label="联系电话" prop="telephone">
                        <el-input v-model="form.telephone" placeholder="请输入联系电话" />
                    </el-form-item>
                    <el-form-item label="联系地址" prop="address">
                        <el-input v-model="form.address" placeholder="请输入联系地址" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" :loading="submitLoading" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
        </div>
    </el-dialog>

    <!-- 客户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
        <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip text-center" slot="tip">
                <div class="el-upload__tip" slot="tip">
                    <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的客户数据
                </div>
                <span>仅允许导入xls、xlsx格式文件。</span>
                <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
            </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
        </div>
    </el-dialog>
</div>
</template>

<script>
import {
    listCustomer,
    getCustomer,
    delCustomer,
    addCustomer,
    updateCustomer
} from "@/api/basicInfo/customer";
import { getToken } from "@/utils/auth";

export default {
    name: "Customer",
    dicts: ['sys_oper_type', 'sys_job_status', 'client_type', 'supplier_level', 'sys_yes_no'],
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 客户表格数据
            customerList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                customerName: null,
                customerCode: null,
                deptId: null,
                customerLevel: null,
                customerType: null,
                status:'Y',
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {
                customerId: [{
                    required: true,
                    message: "客户名称不能为空",
                    trigger: "blur"
                }],
                customerName: [{
                    required: true,
                    message: "客户名称不能为空",
                    trigger: "blur"
                }],
                customerCode: [{
                    required: true,
                    message: "客户编码不能为空",
                    trigger: "blur"
                }],
                telephone: [
                    { required: false, message: "联系电话不能为空", trigger: "blur" },
                    { 
                        pattern: /^((1[3-9]\d{9})|(\d{3,4}-\d{7,8}))$/, 
                        message: "请输入正确的联系电话（手机号或座机号）", 
                        trigger: "blur"
                    }
                ],
                status: [
                    { required: true, message: "启用状态不能为空", trigger: "change" }
                ]
            },
            // 提交按钮加载动画
            submitLoading: false,
            // 客户导入参数
            upload: {
                open: false,
                title: "",
                isUploading: false,
                updateSupport: 0,
                headers: {
                    Authorization: "Bearer " + getToken()
                },
                url: process.env.VUE_APP_BASE_API + "/basicInfo/customer/importData"
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        /** 查询客户列表 */
        getList() {
            this.loading = true;
            listCustomer(this.queryParams).then(response => {
                this.customerList = response.rows;
                this.total = response.total;
                this.loading = false;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                customerId: null,
                customerName: null,
                customerCode: null,
                deptId: null,
                customerLevel: null,
                customerType: null,
                linkMan: null,
                telephone: null,
                address: null,
                email: null,
                spare1: null,
                spare2: null,
                createBy: null,
                createTime: null,
                updateBy: null,
                updateTime: null,
                status:'Y',
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        // 客户状态修改
        handleStatusChange(row) {
            let text = row.status === "Y" ? "启用" : "停用";
            this.$modal.confirm('确认要"' + text + '""' + row.customerName + '"客户吗？').then(() => {
                return updateCustomer(row);
            }).then(() => {
                this.$modal.msgSuccess(text + "成功");
            }).catch(() => {
                row.status = row.status === "Y" ? "N" : "Y";
            });
        },
        // 多选框选中数据
        handleSelectionChange(selection) {
            this.ids = selection.map(item => item.customerId)
            this.single = selection.length !== 1
            this.multiple = !selection.length
        },
        /** 新增按钮操作 */
        async handleAdd() {
            this.reset();
            const res = await this.$getAutoCode();
            this.form.customerCode = res;
            this.open = true;
            this.title = "添加客户";
        },
        /** 修改按钮操作 */
        handleUpdate(row) {
            this.reset();
            const customerId = row.customerId || this.ids
            getCustomer(customerId).then(response => {
                this.form = response.data;
                this.open = true;
                this.title = "修改客户";
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.submitLoading = true;
            this.$refs["form"].validate(valid => {
                if (valid) {
                    if (this.form.customerId != null) {
                        updateCustomer(this.form).then(response => {
                            this.$modal.msgSuccess("修改成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    } else {
                        addCustomer(this.form).then(response => {
                            this.$modal.msgSuccess("新增成功");
                            this.open = false;
                            this.getList();
                        }).finally(() => {
                            this.submitLoading = false;
                        });
                    }
                } else {
                    this.submitLoading = false;
                }
            });
        },
        /** 删除按钮操作 */
        handleDelete(row) {
            const customerIds = row.customerId || this.ids;
            this.$modal.confirm('是否确认删除客户编号为"' + customerIds + '"的数据项？').then(function () {
                return delCustomer(customerIds);
            }).then(() => {
                this.getList();
                this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        },
        /** 导入按钮操作 */
        handleImport() {
            this.upload.title = "客户导入";
            this.upload.open = true;
        },
        /** 下载模板操作 */
        importTemplate() {
            this.download('basicInfo/customer/importTemplate', {}, `customer_template_${new Date().getTime()}.xlsx`)
        },
        // 文件上传中处理
        handleFileUploadProgress(event, file, fileList) {
            this.upload.isUploading = true;
        },
        // 文件上传成功处理
        handleFileSuccess(response, file, fileList) {
            this.upload.open = false;
            this.upload.isUploading = false;
            this.$refs.upload.clearFiles();
            this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {
                dangerouslyUseHTMLString: true
            });
            this.getList();
        },
        // 提交上传文件
        submitFileForm() {
            this.$refs.upload.submit();
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('basicInfo/customer/export', {
                ...this.queryParams
            }, `customer_${new Date().getTime()}.xlsx`)
        }
    }
};
</script>

<!-- <style lang="scss" scoped>
::v-deep.el-loading-spinner {
    left: 50% !important;
}
</style> -->
