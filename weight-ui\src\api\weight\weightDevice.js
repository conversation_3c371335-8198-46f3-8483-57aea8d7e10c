import request from '@/utils/request'

// 查询地磅设备关联列表
export function listWeightDevice(query) {
  return request({
    url: '/weight/weightDevice/list',
    method: 'get',
    params: query
  })
}

// 查询地磅设备关联详细
export function getWeightDevice(weighbridgeCode) {
  return request({
    url: '/weight/weightDevice/' + weighbridgeCode,
    method: 'get'
  })
}

// 新增地磅设备关联
export function addWeightDevice(data) {
  return request({
    url: '/weight/weightDevice',
    method: 'post',
    data: data
  })
}

// 修改地磅设备关联
export function updateWeightDevice(data) {
  return request({
    url: '/weight/weightDevice',
    method: 'put',
    data: data
  })
}

// 删除地磅设备关联
export function delWeightDevice(weighbridgeCode) {
  return request({
    url: '/weight/weightDevice/' + weighbridgeCode,
    method: 'delete'
  })
}
