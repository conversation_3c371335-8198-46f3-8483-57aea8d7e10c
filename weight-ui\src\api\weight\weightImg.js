import request from '@/utils/request'

// 查询过磅图片列表
export function listWeightImg(query) {
  return request({
    url: '/weight/weightImg/list',
    method: 'get',
    params: query
  })
}

// 查询过磅图片详细
export function getWeightImg(weightImgId) {
  return request({
    url: '/weight/weightImg/' + weightImgId,
    method: 'get'
  })
}

// 新增过磅图片
export function addWeightImg(data) {
  return request({
    url: '/weight/weightImg',
    method: 'post',
    data: data
  })
}

// 修改过磅图片
export function updateWeightImg(data) {
  return request({
    url: '/weight/weightImg',
    method: 'put',
    data: data
  })
}

// 删除过磅图片
export function delWeightImg(weightImgId) {
  return request({
    url: '/weight/weightImg/' + weightImgId,
    method: 'delete'
  })
}
